from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import EventTemplate, EventStep
from .serializers import EventTemplateSerializer, EventStepSerializer, EventStepCreateSerializer, EventStepUpdateSerializer

class EventTemplateViewSet(viewsets.ModelViewSet):
    """
    API endpoint for viewing and editing event templates.
    """
    queryset = EventTemplate.objects.all()
    serializer_class = EventTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return EventTemplate.objects.filter(creator=self.request.user)

    def perform_create(self, serializer):
        serializer.save(creator=self.request.user)
    
    @action(detail=True, methods=['post'], url_path='add-step')
    def add_step(self, request, pk=None):
        template = self.get_object()
        
        # --- FIX: Pass the template object to the serializer's context ---
        # The serializer will now handle the logic for creating the step.
        serializer = EventStepCreateSerializer(
            data=request.data,
            context={'template': template} # Pass template in context
        )
        
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], url_path='reorder-steps')
    def reorder_steps(self, request, pk=None):
        """
        Reorder steps within a template.
        Expects: {'step_ids': [3, 1, 2]} - list of step IDs in new order
        """
        template = self.get_object()
        step_ids = request.data.get('step_ids', [])

        if not step_ids:
            return Response({'error': 'step_ids is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Verify all step IDs belong to this template
        template_step_ids = set(template.steps.values_list('id', flat=True))
        provided_step_ids = set(step_ids)

        if template_step_ids != provided_step_ids:
            return Response(
                {'error': 'Provided step_ids do not match template steps'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update the order of each step
        try:
            for new_order, step_id in enumerate(step_ids, start=1):
                EventStep.objects.filter(id=step_id, template=template).update(order=new_order)

            # Return updated template data
            serializer = self.get_serializer(template)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'error': f'Failed to reorder steps: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class EventStepViewSet(viewsets.ModelViewSet):
    """
    API endpoint for individual steps (for editing/deleting).
    """
    queryset = EventStep.objects.all()
    serializer_class = EventStepSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return EventStep.objects.filter(template__creator=self.request.user)

    def get_serializer_class(self):
        """
        Use different serializers for different actions.
        """
        if self.action in ['update', 'partial_update']:
            return EventStepUpdateSerializer
        return EventStepSerializer
