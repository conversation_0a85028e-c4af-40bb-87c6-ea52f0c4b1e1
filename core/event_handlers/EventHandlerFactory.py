from typing import Optional, TYPE_CHECKING

from events.models import EventStep
# Import specific handlers. It's good practice to be explicit.
from .BaseEventHandler import BaseEventHandler
from .PictionaryEventHandler import Pictionary<PERSON>ventHandler
from .FreeChatEventHandler import <PERSON><PERSON>hat<PERSON><PERSON>Handler

# CHANGE: Use TYPE_CHECKING to break the circular dependency.
# This import will only be processed by type checkers, not at runtime.
if TYPE_CHECKING:
    from ..consumers import RoomConsumer

class EventHandlerFactory:
    """环节处理器工厂类"""

    _handlers = {
        EventStep.STEP_GAME_PICTIONARY: PictionaryEventHandler,
        EventStep.STEP_FREE_CHAT: FreeChatEventHandler,
    }

    @classmethod
    def create_handler(cls, step_type: str, room_code: str, consumer: 'RoomConsumer') -> Optional[BaseEventHandler]:
        """
        根据环节类型创建对应的处理器

        Args:
            step_type: 环节类型
            room_code: 房间代码
            consumer: WebSocket消费者实例

        Returns:
            BaseEventHandler: 对应的处理器实例，如果类型不支持则返回None
        """
        handler_class = cls._handlers.get(step_type)
        if handler_class:
            return handler_class(room_code, consumer)
        return None

    @classmethod
    def get_supported_types(cls) -> list:
        """获取支持的环节类型列表"""
        return list(cls._handlers.keys())
