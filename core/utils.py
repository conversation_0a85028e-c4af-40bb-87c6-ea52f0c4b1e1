import logging
from channels.db import database_sync_to_async

# It's assumed that your models.py is in the same app directory.
# Adjust the import path if necessary.
from .models import Room

logger = logging.getLogger(__name__)

@database_sync_to_async
def get_room_with_template(room_code):
    """
    Get room with its event template and participants.
    """
    try:
        return Room.objects.select_related('event_template').prefetch_related('participants').get(room_code=room_code)
    except Room.DoesNotExist:
        return None

def advance_to_next_step_sync(room):
    """
    Advance room to the next step in its event template. (Sync version)
    """
    if not room.event_template:
        return None

    # Get the next step based on current_step_order
    next_step = room.event_template.steps.filter(order__gt=room.current_step_order).order_by('order').first()

    if next_step:
        # Update room's current step order
        room.current_step_order = next_step.order
        room.save()
        return next_step

    return None  # No more steps

# Async wrapper for the sync function
advance_to_next_step = database_sync_to_async(advance_to_next_step_sync)

@database_sync_to_async
def save_room(room):
    """
    Save room instance to the database.
    """
    room.save()
