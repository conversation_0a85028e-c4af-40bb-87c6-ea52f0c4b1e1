import json
import random
import logging
import asyncio

from typing import Optional
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async

from .event_handlers import BaseEventHandler, EventHandlerFactory
from .models import Room, User, RoomMembership
from events.models import EventStep
from .utils import get_room_with_template, advance_to_next_step, save_room

logger = logging.getLogger(__name__)

# ==============================================================================
# --- 数据库助手函数 (这部分没有改动) ---
# ==============================================================================
@database_sync_to_async
def add_user_to_room_membership(room_code, user):
    try:
        room = Room.objects.get(room_code=room_code)
        RoomMembership.objects.get_or_create(user=user, room=room)
    except Room.DoesNotExist:
        logger.warning(f"Room {room_code} not found for user {user.username} to join.")

@database_sync_to_async
def remove_user_from_room_membership(room_code, user):
    if user.is_authenticated:
        RoomMembership.objects.filter(room__room_code=room_code, user=user).delete()

@database_sync_to_async
def set_user_ready_status(room_code, user, is_ready):
    try:
        member = RoomMembership.objects.get(room__room_code=room_code, user=user)
        member.is_ready = is_ready
        member.save()
        logger.info(f"Successfully updated ready status for user {user.username} in room {room_code} to {is_ready}")
    except RoomMembership.DoesNotExist:
        logger.error(f"RoomMembership not found for user {user.username} in room {room_code}")
        raise

@database_sync_to_async
def start_game_logic(room_code, user):
    room = Room.objects.get(room_code=room_code)
    if room.host != user:
        return {"error": "只有房主才能开始游戏"}
    if room.roommembership_set.filter(is_ready=False).exists():
        return {"error": "还有成员尚未准备"}
    return {"success": True}

@database_sync_to_async
def get_full_room_state(room_code):
    try:
        room = Room.objects.get(room_code=room_code)
        members = room.roommembership_set.select_related('user').all()
        return {
            "room_status": room.status,
            "host_id": room.host_id,
            "members": [{"id": m.user.id, "username": m.user.username, "is_ready": m.is_ready} for m in members]
        }
    except Room.DoesNotExist:
        return None

# ==============================================================================
# --- 主消费者类 (MAIN CONSUMER CLASS) ---
# ==============================================================================

class RoomConsumer(AsyncWebsocketConsumer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.timer_task: Optional[asyncio.Task] = None
        self.current_handler: Optional[BaseEventHandler] = None

    async def connect(self):
        self.user = self.scope['user']
        if not self.user.is_authenticated:
            await self.close()
            return

        self.room_code = self.scope['url_route']['kwargs']['room_code']
        self.room_group_name = f'room_{self.room_code}'

        await self.channel_layer.group_add(self.room_group_name, self.channel_name)
        await self.accept()
        
        await add_user_to_room_membership(self.room_code, self.user)
        await self.broadcast_room_state()
        
        logger.info(f"User {self.user.username} connected to room {self.room_code}")

    async def disconnect(self, close_code):
        if self.timer_task and not self.timer_task.done():
            self.timer_task.cancel()
        if self.current_handler:
            await self.current_handler.cleanup()
        if hasattr(self, 'room_group_name'):
            await remove_user_from_room_membership(self.room_code, self.user)
            await self.broadcast_room_state()
            await self.channel_layer.group_discard(self.room_group_name, self.channel_name)
        logger.info(f"User {self.scope['user'].username} disconnected from room {self.room_code}")

    async def receive(self, text_data):
        """
        【重构后】统一的事件接收和分发方法。
        """
        try:
            data = json.loads(text_data)
            action = data.get('action')
            payload = data.get('payload', {})
            user = self.scope['user']

            logger.info(f"Received action '{action}' from user {user.username} in room {self.room_code}")
            
            # --- 1. 优先委托给当前环节的处理器 ---
            if self.current_handler:
                handled = await self.current_handler.handle_action(action, user, payload)
                if handled:
                    return

            # --- 2. 如果处理器不处理，则由Consumer处理全局或通用action ---
            if action == 'set_ready':
                await self.handle_set_ready(payload)
            elif action == 'start_game':
                await self.handle_start_game()
            elif action == 'next_step':
                await self.handle_next_step()
            elif action == 'restart_game':
                await self.handle_restart_game(payload)
            elif action == 'send_message':
                await self.handle_chat_message(payload)
            else:
                logger.warning(f"Unknown or unhandled action '{action}' from user {user.username}")
                await self.send_error(f"Unknown or unhandled action: {action}")

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON received from user {self.scope['user'].username}: {e}")
            await self.send_error("Invalid message format")
        except Exception as e:
            logger.error(f"Error processing message from user {self.scope['user'].username}: {e}", exc_info=True)
            await self.send_error("Internal server error")
            
    # --- handle_... 方法 (这部分没有改动) ---
    async def handle_set_ready(self, payload):
        is_ready = payload.get('is_ready', False)
        try:
            await set_user_ready_status(self.room_code, self.user, is_ready)
            await self.broadcast_room_state()
        except Exception as e:
            logger.error(f"Error setting ready status for user {self.user.username}: {e}")
            await self.send_error(f"Failed to set ready status: {str(e)}")

    async def handle_start_game(self):
        result = await start_game_logic(self.room_code, self.user)
        if result.get("error"):
            await self.send_error(result['error'])
        else:
            await self.handle_next_step()

    async def handle_next_step(self):
        try:
            user = self.scope['user']
            room = await get_room_with_template(self.room_code)
            if not room:
                await self.send_error('房间不存在。'); return
            room_host = await database_sync_to_async(lambda: room.host)()
            if room_host != user:
                await self.send_error('只有房主才能开始下一环节。'); return
            
            next_step = await advance_to_next_step(room)
            if not next_step:
                await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_event_over'}); return
            
            # 【注意】这里只在房主的Consumer里创建了一个临时的Handler
            # 用来初始化游戏数据，它不会被赋值给self.current_handler
            temp_handler = EventHandlerFactory.create_handler(next_step.step_type, self.room_code, self)
            
            if not temp_handler:
                await self.send_error(f"不支持的环节类型: {next_step.step_type}"); return
            
            # 初始化环节，获取游戏数据
            game_data, error = await temp_handler.start_step(room, next_step)
            if error:
                await self.send_error(error); return
            
            # 【关键】将带有环节类型的数据广播给房间里的所有人
            # 真正的 self.current_handler 将在 broadcast_step_start 中被设置
            await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_step_start', 'payload': game_data})
            await self.start_step_timer(next_step.duration)
        except Exception as e:
            logger.error(f"Error handling next_step in room {self.room_code}: {e}", exc_info=True)
            await self.send_error("处理下一环节时发生错误。")
            
    async def handle_chat_message(self, payload):
        try:
            message = payload.get('message', '').strip()
            if not message: return
            await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_chat_message', 'payload': {'message': message, 'sender': self.scope['user'].username}})
        except Exception as e:
            logger.error(f"Error handling chat message from user {self.scope['user'].username} in room {self.room_code}: {e}")
            await self.send_error("发送消息时发生错误。")
            
    async def handle_restart_game(self, payload):
        try:
            user = self.scope['user']
            room = await get_room_with_template(self.room_code)
            if not room: await self.send_error('房间不存在。'); return
            room_host = await database_sync_to_async(lambda: room.host)()
            if room_host != user: await self.send_error('只有房主才能重新开始游戏。'); return
            
            if self.current_handler:
                game_data, error = await self.current_handler.handle_restart(user, payload)
                if error: await self.send_error(error); return
                if game_data:
                    await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_step_start', 'payload': game_data})
                    current_step = await database_sync_to_async(lambda: room.event_template.steps.filter(order=room.current_step_order).first())()
                    if current_step: await self.start_step_timer(current_step.duration)
            else:
                await self.send_error("当前没有活动的环节可以重启。")
        except Exception as e:
            logger.error(f"Error handling restart game from user {user.username} in room {self.room_code}: {e}")
            await self.send_error("重新开始游戏时发生错误。")

    async def start_step_timer(self, duration_seconds):
        if self.timer_task and not self.timer_task.done(): self.timer_task.cancel()
        if duration_seconds > 0:
             self.timer_task = asyncio.create_task(self._step_timer(duration_seconds))

    async def _step_timer(self, duration_seconds):
        try:
            await asyncio.sleep(duration_seconds)
            await self.handle_step_timeout()
        except asyncio.CancelledError:
            logger.info(f"Timer cancelled for room {self.room_code}"); raise

    async def handle_step_timeout(self):
        try:
            if self.current_handler:
                await self.current_handler.handle_timeout()
            else:
                room = await get_room_with_template(self.room_code)
                if room:
                    room.status = Room.STATUS_WAITING
                    await save_room(room)
                    await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_step_timeout', 'payload': {'room_status': room.status}})
        except Exception as e:
            logger.error(f"Error handling step timeout for room {self.room_code}: {e}")

    async def send_error(self, message):
        await self.send(text_data=json.dumps({'type': 'error', 'payload': {'message': message}}))

    # --- 广播和消息处理器 ---
    async def broadcast_room_state(self):
        room_state = await get_full_room_state(self.room_code)
        if room_state:
            await self.channel_layer.group_send(
                self.room_group_name,
                {'type': 'room_state_update', 'payload': room_state}
            )
            
    async def room_state_update(self, event):
        await self.send(text_data=json.dumps({'type': 'room_state_update', 'payload': event['payload']}))

    async def broadcast_step_start(self, event):
        """
        【关键改动】当收到环节开始的广播时，所有Consumer实例都会执行此方法。
        """
        payload = event['payload'].copy()
        step_type = payload.get('step_info', {}).get('step_type')

        # 1. 为每个Consumer实例创建并设置正确的Handler
        if step_type:
            if self.current_handler:
                await self.current_handler.cleanup()
            self.current_handler = EventHandlerFactory.create_handler(step_type, self.room_code, self)
            logger.info(f"User {self.user.username}'s consumer now has handler: {type(self.current_handler).__name__}")
        else:
            # 如果没有handler，清理掉旧的
            if self.current_handler:
                await self.current_handler.cleanup()
                self.current_handler = None
            logger.warning(f"Step start broadcast for room {self.room_code} without a step_type.")

        # 2. 对非绘画者隐藏你画我猜的词语
        if step_type == EventStep.STEP_GAME_PICTIONARY:
            if self.scope['user'].username != payload['drawer']:
                word = payload.get('word', '')
                # 保持原有的、虽然有瑕疵但可用的隐藏逻辑
                payload['word'] = " ".join(["_" for char in word if char != ' '])
        
        # 3. 将最终的payload发送给客户端
        await self.send(text_data=json.dumps({'type': 'step_started', 'payload': payload}))

    async def broadcast_chat_message(self, event):
        await self.send(text_data=json.dumps({'type': 'chat_message', 'payload': event['payload']}))

    async def broadcast_drawing_data(self, event):
        await self.send(text_data=json.dumps({'type': 'drawing_data', 'payload': event['payload']}))

    async def broadcast_round_over(self, event):
        await self.send(text_data=json.dumps({'type': 'round_over', 'payload': event['payload']}))

    async def broadcast_step_timeout(self, event):
        await self.send(text_data=json.dumps({'type': 'step_timeout', 'payload': event['payload']}))
        
    async def broadcast_event_over(self, _event):
        await self.send(text_data=json.dumps({'type': 'event_finished', 'payload': {'message': '所有环节已结束！感谢您的参与。'}}))
