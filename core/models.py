from django.db import models
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from events.models import EventTemplate

class User(AbstractUser):
    # 新增：为用户添加“钱包”，用于每日签到奖励
    currency = models.IntegerField(default=0, verbose_name="虚拟货币")
    
    # 原有的groups和user_permissions保持不变
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text=('The groups this user belongs to. A user will get all permissions '
                   'granted to each of their groups.'),
        related_name="core_user_set",
        related_query_name="user",
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name="core_user_permissions_set",
        related_query_name="user",
    )

    def __str__(self):
        return self.username

# 新增：“签到记录本”模型
class CheckIn(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='check_ins')
    check_in_date = models.DateField(auto_now_add=True)
    class Meta:
        unique_together = ('user', 'check_in_date')
    def __str__(self):
        return f"{self.user.username} checked in on {self.check_in_date}"

# 新增：“房间成员”及准备状态模型
class RoomMembership(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    room = models.ForeignKey('Room', on_delete=models.CASCADE)
    is_ready = models.BooleanField(default=False)
    joined_at = models.DateTimeField(auto_now_add=True)
    class Meta:
        unique_together = ('room', 'user')
    def __str__(self):
        return f"{self.user.username} in Room {self.room.room_code} - Ready: {self.is_ready}"

class Room(models.Model):
    STATUS_WAITING = 'WAITING'
    STATUS_IN_PROGRESS = 'IN_PROGRESS'
    STATUS_FINISHED = 'FINISHED'
    STATUS_CHOICES = [
        (STATUS_WAITING, 'Waiting for players'),
        (STATUS_IN_PROGRESS, 'In Progress'),
        (STATUS_FINISHED, 'Finished'),
    ]

    room_code = models.CharField(max_length=10, unique=True, blank=True)
    host = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='hosted_rooms')
    
    # 改造：使用RoomMembership作为中间模型来增强participants
    participants = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        through='RoomMembership',
        related_name='joined_rooms',
        blank=True
    )
    
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_WAITING)
    created_at = models.DateTimeField(auto_now_add=True)
    event_template = models.ForeignKey(EventTemplate, on_delete=models.SET_NULL, null=True, blank=True)
    current_step_order = models.PositiveIntegerField(default=0)

    def __str__(self):
        template_name = self.event_template.name if self.event_template else "No Template"
        return f"Room {self.room_code} ({template_name})"

class GameSession(models.Model):
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='game_sessions')
    is_active = models.BooleanField(default=True)