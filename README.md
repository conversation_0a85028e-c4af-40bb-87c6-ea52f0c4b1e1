# 团子 (Tuanzi) - 团建活动集成APP

*我们团，你们玩*

---

## 1. 项目简介

**团子**是一个集成的手机APP，旨在解决团队活动（如公司团建、班级活动、朋友聚会）中可能遇到的所有事务。其核心是高度可定制的**"环节" (Events/Flow) 系统**，允许主持人像编排剧本一样，将不同的游戏、讨论、投票等活动组合成一个完整的、可复用的团建流程。

## 2. 项目状态 (截至 2025年7月4日)

**当前阶段**: **阶段二：功能拓展** (中期)

我们已经完成了MVP的开发、第一阶段的架构重构，以及核心功能的稳定性改进。应用目前拥有一个稳定、可扩展的框架，并成功集成了完整的核心玩法。

-   **已完成**:
    -   ✅ **核心架构**: 基于Django Channels和React Native的实时通信架构
    -   ✅ **用户系统**: 支持用户注册和基于JWT的登录认证
    -   ✅ **房间系统**: 支持创建房间、加入房间，并以"环节"为核心驱动流程
    -   ✅ **环节设计器 (V1)**: 支持创建环节模板、添加环节步骤（你画我猜/自由聊天）、删除步骤
    -   ✅ **你画我猜游戏**: 完整的绘图、猜词、计分、多轮游戏逻辑，包含性能优化和错误处理
    -   ✅ **自由讨论功能**: 实时聊天系统，支持权限管理的操作面板
    -   ✅ **权限管理系统**: 基于角色的操作权限控制（房主/管理员/参与者）
    -   ✅ **WebSocket连接优化**: 自动重连、错误处理、连接状态管理
    -   ✅ **类型安全改进**: 完善的TypeScript类型定义和接口规范
    -   ✅ **环节设计器 (V2)**: 完善环节设计器，增加步骤的编辑和拖拽排序功能

-   **当前重点 / 下一步工作**:
    -   **[IN_PROGRESS]** 签到、点名功能
    -   **[PLANNED]** 开发更多环节类型，如"谁是卧底"、投票、问答等
    -   **[PLANNED]** 添加单元测试和集成测试
    -   **[PLANNED]** 性能监控和优化

## 3. 技术栈

-   **后端**: Python, Django, Django REST Framework, Django Channels, Daphne (ASGI Server)
-   **前端**: React, React Native, TypeScript
-   **核心库**: React Navigation, react-native-svg, react-native-gesture-handler
-   **数据库**: SQLite (开发), PostgreSQL (生产)

## 4. 快速开始 (一站式启动)

为了简化开发流程，我们配置了自动化脚本。

1.  **配置IP地址 (仅在更换网络时需要)**:
    ```bash
    cd TuanziApp
    npm run config:ip
    ```

2.  **启动所有服务**:
    在一个终端窗口中，运行以下命令来同时启动后端服务器和前端Metro打包器。
    ```bash
    cd TuanziApp
    npm run dev
    ```

3.  **编译并运行App**:
    在**另一个**终端窗口中，运行：
    ```bash
    cd TuanziApp
    npm run android
    ```

## 5. 核心功能特性

### 🎮 游戏系统
- **你画我猜**: 实时绘图、词汇猜测、计分系统
- **自由讨论**: 实时聊天、权限管理操作面板
- **可扩展架构**: 支持快速添加新游戏类型

### 🔧 技术特性
- **实时通信**: 基于WebSocket的低延迟通信
- **自动重连**: 网络断开自动重连机制
- **性能优化**: 绘图节流、消息批处理
- **类型安全**: 完整的TypeScript类型定义
- **错误处理**: 全面的错误捕获和用户友好提示

### 👥 权限管理
- **房主**: 完整的房间控制权限
- **管理员**: 预留的中级权限角色
- **参与者**: 基础的游戏参与权限

## 6. 详细文档

-   [后端设置与架构 (Tuanzi_Backend/README.md)](./Tuanzi_Backend/README.md)
-   [前端设置与架构 (TuanziApp/README.md)](./TuanziApp/README.md)
-   [模块扩展指南 (CONTRIBUTING.md)](./CONTRIBUTING.md)

## 7. 开发团队

-   **开发者A (gellar)**: 负责项目架构、核心后端功能
-   **开发者B**: 负责用户界面、核心游戏功能
-   **设计师**: 提供美术意见和UI/UX指导

---
# 调试说明：
如果发生了奇怪的问题，看上去和你刚刚添加的代码半毛钱关系没有，先不着急求助AI，先按照一下工序排查：
1. 重启后端服务器
```bash
   source venv/bin/activate
   daphne -b 0.0.0.0 -p 8000 Tuanzi_Backend.asgi:application 
```

2. 重新下载APP
```bash
    npm run android
```

3. 重新配置ip
```bash
    npm run config:ip
```

4. 重新启动metro服务
```bash
    npm run dev
```

5. 重新迁移数据库
    数据库文件db.sqlite3没有被git跟踪，目的就是不要让本地的数据库污染云端，因此如果涉及到牵扯数据库的更改，一定要手动重新迁移一遍
```bash
    source venv/bin/activate
    python manage.py migrate
    python manage.py makemigrations
``` 

---

*最后更新: 2025年7月9日*
