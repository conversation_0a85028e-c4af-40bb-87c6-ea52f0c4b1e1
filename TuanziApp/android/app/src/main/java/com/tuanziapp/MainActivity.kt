package com.tuanziapp

import android.os.Bundle;
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultReactActivityDelegate

class MainActivity : ReactActivity() {

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = "TuanziApp"

  /**
   * Returns the instance of the [ReactActivityDelegate].
   * We are overriding this to explicitly disable the New Architecture.
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate {
    // This is the key change.
    // We explicitly pass 'false' to the constructor to disable <PERSON><PERSON><PERSON> (New Architecture).
    // This prevents the app from trying to load the native libraries that were causing the crash.
    return DefaultReactActivityDelegate(this, mainComponentName, false)
  }

  /**
   * This override is required for an issue with react-native-screens.
   * It should be added to your MainActivity.kt file.
   */
  override fun onCreate(savedInstanceState: Bundle?) {
    // We pass null here to avoid a state restoration crash. This is your original fix.
    super.onCreate(null)
  }
}