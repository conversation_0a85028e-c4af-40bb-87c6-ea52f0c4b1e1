/**
 * Tests for EditStepScreen component
 */
import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { EditStepScreen } from '../src/screens/EventDesigner/EditStepScreen';
import { AuthContext } from '../src/auth/AuthContext';
import * as eventApi from '../src/api/eventApi';

// Mock the API
jest.mock('../src/api/eventApi');
const mockedEventApi = eventApi as jest.Mocked<typeof eventApi>;

// Mock navigation
const mockNavigate = jest.fn();
const mockGoBack = jest.fn();

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    goBack: mockGoBack,
  }),
  useRoute: () => ({
    params: { stepId: 1 },
  }),
}));

const Stack = createStackNavigator();

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const authValue = {
    user: { username: 'testuser' },
    token: 'test-token',
    login: jest.fn(),
    logout: jest.fn(),
    restoreUser: jest.fn(),
  };

  return (
    <AuthContext.Provider value={authValue}>
      <NavigationContainer>
        <Stack.Navigator>
          <Stack.Screen name="EditStep" component={() => children} />
        </Stack.Navigator>
      </NavigationContainer>
    </AuthContext.Provider>
  );
};

describe('EditStepScreen', () => {
  const mockStep = {
    id: 1,
    name: 'Test Step',
    order: 1,
    step_type: 'GAME_PICTIONARY' as const,
    configuration: {},
    duration: 300,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockedEventApi.getStepDetails.mockResolvedValue(mockStep);
    mockedEventApi.updateStep.mockResolvedValue(mockStep);
  });

  it('renders correctly and loads step data', async () => {
    const { getByText, getByDisplayValue } = render(
      <TestWrapper>
        <EditStepScreen />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(getByText('编辑步骤')).toBeTruthy();
      expect(getByDisplayValue('Test Step')).toBeTruthy();
      expect(getByDisplayValue('5')).toBeTruthy(); // 300 seconds = 5 minutes
    });

    expect(mockedEventApi.getStepDetails).toHaveBeenCalledWith('test-token', 1);
  });

  it('updates step name input', async () => {
    const { getByDisplayValue } = render(
      <TestWrapper>
        <EditStepScreen />
      </TestWrapper>
    );

    await waitFor(() => {
      const nameInput = getByDisplayValue('Test Step');
      fireEvent.changeText(nameInput, 'Updated Step Name');
      expect(nameInput.props.value).toBe('Updated Step Name');
    });
  });

  it('updates duration input', async () => {
    const { getByDisplayValue } = render(
      <TestWrapper>
        <EditStepScreen />
      </TestWrapper>
    );

    await waitFor(() => {
      const durationInput = getByDisplayValue('5');
      fireEvent.changeText(durationInput, '10');
      expect(durationInput.props.value).toBe('10');
    });
  });

  it('selects different step type', async () => {
    const { getByText } = render(
      <TestWrapper>
        <EditStepScreen />
      </TestWrapper>
    );

    await waitFor(() => {
      const freeChatOption = getByText('自由讨论');
      fireEvent.press(freeChatOption);
      // Check if the option is selected (would need to check styling)
    });
  });

  it('saves step changes successfully', async () => {
    const { getByText, getByDisplayValue } = render(
      <TestWrapper>
        <EditStepScreen />
      </TestWrapper>
    );

    await waitFor(() => {
      // Update name
      const nameInput = getByDisplayValue('Test Step');
      fireEvent.changeText(nameInput, 'Updated Step');

      // Update duration
      const durationInput = getByDisplayValue('5');
      fireEvent.changeText(durationInput, '10');

      // Save changes
      const saveButton = getByText('保存更改');
      fireEvent.press(saveButton);
    });

    await waitFor(() => {
      expect(mockedEventApi.updateStep).toHaveBeenCalledWith('test-token', 1, {
        name: 'Updated Step',
        step_type: 'GAME_PICTIONARY',
        duration: 600, // 10 minutes = 600 seconds
      });
    });
  });

  it('shows validation error for invalid duration', async () => {
    const { getByText, getByDisplayValue } = render(
      <TestWrapper>
        <EditStepScreen />
      </TestWrapper>
    );

    await waitFor(() => {
      // Set invalid duration
      const durationInput = getByDisplayValue('5');
      fireEvent.changeText(durationInput, '-5');

      // Try to save
      const saveButton = getByText('保存更改');
      fireEvent.press(saveButton);
    });

    // Should show alert (would need to mock Alert.alert to test this properly)
    expect(mockedEventApi.updateStep).not.toHaveBeenCalled();
  });

  it('handles API error gracefully', async () => {
    mockedEventApi.updateStep.mockRejectedValue(new Error('API Error'));

    const { getByText } = render(
      <TestWrapper>
        <EditStepScreen />
      </TestWrapper>
    );

    await waitFor(() => {
      const saveButton = getByText('保存更改');
      fireEvent.press(saveButton);
    });

    // Should handle error gracefully (would need to mock Alert.alert)
    await waitFor(() => {
      expect(mockedEventApi.updateStep).toHaveBeenCalled();
    });
  });
});
