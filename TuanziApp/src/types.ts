// src/types.ts
import { NavigatorScreenParams } from '@react-navigation/native';

// --- Data Structure Types ---

// Room status constants (保持不变)
export const ROOM_STATUS = {
  WAITING: 'WAITING',
  IN_PROGRESS: 'IN_PROGRESS',
  FINISHED: 'FINISHED',
} as const;
export type RoomStatus = typeof ROOM_STATUS[keyof typeof ROOM_STATUS];

// Step type constants (保持不变)
export const STEP_TYPES = {
  GAME_PICTIONARY: 'GAME_PICTIONARY',
  FREE_CHAT: 'FREE_CHAT',
} as const;
export type StepType = typeof STEP_TYPES[keyof typeof STEP_TYPES];

// 【修正】User类型，现在包含id，以匹配后端数据库模型
export interface User {
  id: number;
  username: string;
  role?: UserRole; // role字段保留
}

// 【修正】Room类型，host现在是一个完整的User对象，并移除了旧的participants
export type Room = {
  id: number;
  room_code: string;
  host: User; // host现在是一个User对象，而不仅仅是字符串
  status: RoomStatus;
  // participants: string[]; // 这个字段已被新的RoomMembership模型代替
};

// 【新增】房间成员的类型，这是准备系统的核心
export interface Member {
  id: number;
  username: string;
  is_ready: boolean;
  is_host?: boolean; // 可选，方便在前端标识房主
}

// 【新增】绘图路径数据的类型，以确保类型安全
export interface PathData {
    id: string;
    color: string;
    strokeWidth: number;
    path: string[];
}

// Message, EventStep, EventTemplate 类型保持不变
export type Message = {
  sender: string;
  message: string;
  timestamp?: string;
};
export type EventStep = {
    id: number;
    name: string;
    order: number;
    step_type: StepType;
    configuration: Record<string, unknown>;
    duration: number;
};
export type EventTemplate = {
    id: number;
    name: string;
    description: string;
    creator_username: string;
    created_at: string;
    steps: EventStep[];
};

// --- WebSocket Message Types ---

export const WS_MESSAGE_TYPES = {
  STEP_STARTED: 'step_started',
  ROUND_OVER: 'round_over',
  EVENT_FINISHED: 'event_finished',
  ERROR: 'error',
  CHAT_MESSAGE: 'chat_message',
  DRAWING_DATA: 'drawing_data',
  STEP_TIMEOUT: 'step_timeout',
} as const;

export type WSMessageType = typeof WS_MESSAGE_TYPES[keyof typeof WS_MESSAGE_TYPES];

export interface WSMessage<T = unknown> {
  type: WSMessageType;
  payload: T;
}

export interface StepStartedPayload {
  step_info: EventStep;
  room_status: RoomStatus;
  drawer?: string;
  word?: string;
  duration?: number;
}

export interface RoundOverPayload {
  winner?: string;
  word: string;
  room_status: RoomStatus;
  scores: Record<string, number>;
  timeout?: boolean;
}

export interface ChatMessagePayload {
  sender: string;
  message: string;
}

export interface DrawingDataPayload {
  path_data: {
    id: string;
    path: string;
    color: string;
  };
}

// 你画我猜游戏状态
export interface PictionaryState {
  drawer: string;
  word: string;
  duration?: number;
}

export interface ErrorPayload {
  message: string;
}

// --- User and Permission Types (保持不变) ---
export const USER_ROLES = {
  HOST: 'host',
  ADMIN: 'admin',
  PARTICIPANT: 'participant',
} as const;
export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];


// --- Navigation Param List ---
// 【修正】确保Room参数的类型与我们更新后的Room类型一致
export type RootStackParamList = {
  Home: undefined;
  Register: undefined;
  Login: undefined;
  CreateRoom: undefined;
  Room: { room: Room }; // 这里的Room现在是我们上面定义的、更准确的类型
  EventDesigner: undefined;
  CreateTemplate: undefined;
  TemplateDetail: { templateId: number };
  AddStep: { templateId: number };
  EditStep: { stepId: number };
  ReadyTest: undefined;
};
