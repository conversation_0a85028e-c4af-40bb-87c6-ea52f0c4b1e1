import React, { useState, useCallback } from 'react';
import { View, Text, Button, StyleSheet, TextInput, Alert, TouchableOpacity } from 'react-native';
import { useAuth } from '../auth/AuthContext';
import { API_URL } from '../api/client';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

export const HomeScreen = () => {
  const { logout, token } = useAuth();
  const [roomCodeInput, setRoomCodeInput] = useState('');
  const navigation = useNavigation<HomeScreenNavigationProp>();

  // --- 【新增】为新功能创建的状态变量 ---
  const [username, setUsername] = useState('');
  const [currency, setCurrency] = useState(0);
  const [hasCheckedIn, setHasCheckedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true); // 用于在数据加载时提供反馈

  // --- 【新增】获取所有用户和签到状态的函数 ---
  const fetchAllStatus = useCallback(async () => {
    if (!token) return;
    setIsLoading(true);
    try {
      // 1. 获取通用的用户状态（包括金币和用户名）
      const userStatusResponse = await fetch(`${API_URL}/api/user/status/`, {
        headers: { 'Authorization': `Bearer ${token}` },
      });
      const userStatusData = await userStatusResponse.json();
      if (userStatusResponse.ok && userStatusData.logged_in) {
        setCurrency(userStatusData.user.currency);
        setUsername(userStatusData.user.username);
      }

      // 2. 获取当天的签到状态
      const checkInStatusResponse = await fetch(`${API_URL}/api/check-in/status/`, {
        headers: { 'Authorization': `Bearer ${token}` },
      });
      const checkInStatusData = await checkInStatusResponse.json();
      if (checkInStatusResponse.ok) {
        setHasCheckedIn(checkInStatusData.checked_in_today);
      }

    } catch (error) {
      console.error("Failed to fetch status:", error);
      Alert.alert("错误", "无法获取最新状态，请检查网络连接。");
    } finally {
      setIsLoading(false); // 无论成功失败，都结束加载状态
    }
  }, [token]);

  // --- 【新增】使用useFocusEffect确保每次用户回到这个屏幕时都刷新数据 ---
  useFocusEffect(
    useCallback(() => {
      fetchAllStatus();
    }, [fetchAllStatus])
  );

  // --- 【新增】处理签到点击的函数 ---
  const handleCheckIn = async () => {
    setIsLoading(true); // 开始签到，禁用按钮
    try {
      const response = await fetch(`${API_URL}/api/check-in/`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` },
      });
      const data = await response.json();
      if (response.ok) {
        Alert.alert('签到成功', data.message);
        setCurrency(data.new_currency); // 更新前端显示的货币
        setHasCheckedIn(true); // 将按钮状态更新为“已签到”
      } else {
        Alert.alert('签到提示', data.error || '签到失败');
        setHasCheckedIn(true); // 如果提示已签到，也更新按钮状态
      }
    } catch (error) {
      Alert.alert('错误', '签到时发生网络错误。');
    } finally {
      setIsLoading(false); // 结束签到，恢复按钮状态
    }
  };

  // --- 原有的 handleJoinRoom 函数保持不变 ---
  const handleJoinRoom = async () => {
    if (!roomCodeInput) return;
    try {
      const response = await fetch(`${API_URL}/api/rooms/join/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
        body: JSON.stringify({ room_code: roomCodeInput }),
      });
      const data = await response.json();
      if (response.ok) {
        navigation.navigate('Room', { room: data });
      } else {
        Alert.alert('加入失败', data.error || '无法加入房间。');
      }
    } catch (error) {
      console.error(error);
      Alert.alert('错误', '加入房间时发生错误。');
    }
  };

  // --- 【改造】渲染界面的部分 ---
  return (
    <View style={styles.container}>
      {/* 新增的用户信息和签到区域 */}
      <View style={styles.header}>
        <Text style={styles.welcomeText}>欢迎, {username || '...'}!</Text>
        <Text style={styles.currencyText}>💰 金币: {currency}</Text>
      </View>
      <TouchableOpacity 
        style={[styles.checkInButton, (hasCheckedIn || isLoading) && styles.disabledButton]}
        onPress={handleCheckIn}
        disabled={hasCheckedIn || isLoading}
      >
        <Text style={styles.checkInButtonText}>{isLoading ? '加载中...' : (hasCheckedIn ? '今日已签到' : '每日签到')}</Text>
      </TouchableOpacity>
      
      <Text style={styles.divider}>- 开启你的团建之旅 -</Text>

      {/* 原有的其他按钮 */}
      <View style={styles.buttonContainer}>
        <Button title="创建新房间" onPress={() => navigation.navigate('CreateRoom')} />
      </View>
      <View style={styles.buttonContainer}>
        <Button title="打开环节设计器" onPress={() => navigation.navigate('EventDesigner')} />
      </View>
      <View style={styles.buttonContainer}>
        <Button title="🔧 准备功能调试" onPress={() => navigation.navigate('ReadyTest')} color="#666" />
      </View>

      <View style={styles.joinSection}>
        <Text style={styles.subtitle}>或加入一个房间</Text>
        <TextInput style={styles.input} placeholder="输入房间代码" value={roomCodeInput} onChangeText={setRoomCodeInput} autoCapitalize="characters" maxLength={6} />
        <Button title="加入房间" onPress={handleJoinRoom} />
      </View>

      <View style={styles.logoutButton}>
        <Button title="登出" onPress={logout} color="#dc3545" />
      </View>
    </View>
  );
};

// --- 【改造】样式表部分，为新元素增加了样式 ---
const styles = StyleSheet.create({
  container: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 },
  header: { alignItems: 'center', marginBottom: 10 },
  welcomeText: { fontSize: 28, fontWeight: 'bold' },
  currencyText: { fontSize: 18, color: '#6c757d', marginTop: 8 },
  checkInButton: { backgroundColor: '#28a745', paddingVertical: 12, paddingHorizontal: 40, borderRadius: 25, marginTop: 20 },
  disabledButton: { backgroundColor: '#aaa' },
  checkInButtonText: { color: 'white', fontSize: 16, fontWeight: 'bold' },
  divider: { color: '#ccc', marginVertical: 30, fontSize: 16 },
  subtitle: { fontSize: 18, marginBottom: 15 },
  input: { width: '80%', height: 40, borderColor: 'gray', borderWidth: 1, borderRadius: 5, marginBottom: 10, paddingHorizontal: 10, textAlign: 'center' },
  joinSection: { width: '100%', marginTop: 20, alignItems: 'center' },
  buttonContainer: { marginBottom: 10, width: '80%' },
  logoutButton: { position: 'absolute', bottom: 40 },
});