import React, { useState } from 'react';
import { View, Text, Button, StyleSheet, ScrollView, Alert } from 'react-native';
import { API_URL } from '../api/client';
import { testNetworkConnection, testLoginAPI, testRegisterAPI, NetworkTestResult } from '../utils/networkTest';

export const NetworkTestScreen = () => {
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testBasicConnection = async () => {
    addResult('🔗 测试基本连接...');
    const result = await testNetworkConnection();
    addResult(`${result.success ? '✅' : '❌'} ${result.message}`);
    if (result.details) {
      addResult(`   详情: ${JSON.stringify(result.details)}`);
    }
  };

  const testLogin = async () => {
    addResult('🔐 测试登录API...');
    const result = await testLoginAPI('test', 'test');
    addResult(`${result.success ? '✅' : '❌'} ${result.message}`);
    if (result.details) {
      addResult(`   详情: ${JSON.stringify(result.details)}`);
    }
  };

  const testRegister = async () => {
    addResult('📝 测试注册API...');
    const randomUser = `testuser_${Date.now()}`;
    const result = await testRegisterAPI(randomUser, 'testpass123');
    addResult(`${result.success ? '✅' : '❌'} ${result.message}`);
    if (result.details) {
      addResult(`   详情: ${JSON.stringify(result.details)}`);
    }
  };

  const runAllTests = async () => {
    setTestResults([]);
    addResult(`🚀 开始网络测试，API URL: ${API_URL}`);
    
    await testBasicConnection();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testLogin();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testRegister();
    
    addResult('🏁 所有测试完成');
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>网络连接测试</Text>
      <Text style={styles.apiUrl}>API URL: {API_URL}</Text>
      
      <View style={styles.buttonContainer}>
        <Button title="运行所有测试" onPress={runAllTests} />
        <Button title="清除结果" onPress={clearResults} />
      </View>
      
      <View style={styles.individualTests}>
        <Button title="测试连接" onPress={testBasicConnection} />
        <Button title="测试登录" onPress={testLogin} />
        <Button title="测试注册" onPress={testRegister} />
      </View>
      
      <ScrollView style={styles.resultsContainer}>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  apiUrl: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  individualTests: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 5,
  },
  resultText: {
    fontSize: 12,
    marginBottom: 5,
    fontFamily: 'monospace',
  },
});
