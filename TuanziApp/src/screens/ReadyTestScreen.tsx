import React, { useState, useEffect, useRef } from 'react';
import { View, Text, Button, StyleSheet, ScrollView, Alert } from 'react-native';
import { useAuth } from '../auth/AuthContext';
import { API_URL, WEBSOCKET_URL_BASE } from '../api/client';

export const ReadyTestScreen = () => {
  const { user, token } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [members, setMembers] = useState<any[]>([]);
  const [logs, setLogs] = useState<string[]>([]);
  const ws = useRef<WebSocket | null>(null);
  
  const roomCode = 'TESTREADY';

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`${timestamp}: ${message}`, ...prev]);
    console.log(message);
  };

  const connectWebSocket = () => {
    if (!token) {
      addLog('❌ 没有token，无法连接');
      return;
    }

    const wsUrl = `${WEBSOCKET_URL_BASE}/ws/room/${roomCode}/?token=${token}`;
    addLog(`🔗 连接WebSocket: ${wsUrl}`);

    if (ws.current) {
      ws.current.close();
    }

    ws.current = new WebSocket(wsUrl);

    ws.current.onopen = () => {
      addLog('✅ WebSocket连接成功');
      setIsConnected(true);
    };

    ws.current.onmessage = (e) => {
      try {
        const data = JSON.parse(e.data);
        addLog(`📨 收到消息: ${JSON.stringify(data)}`);
        
        if (data.type === 'room_state_update') {
          setMembers(data.payload.members || []);
          addLog(`👥 更新成员列表: ${JSON.stringify(data.payload.members)}`);
        }
      } catch (error) {
        addLog(`❌ 解析消息失败: ${error}`);
      }
    };

    ws.current.onclose = (e) => {
      addLog(`🔌 WebSocket连接关闭: ${e.code} - ${e.reason}`);
      setIsConnected(false);
    };

    ws.current.onerror = (error) => {
      addLog(`❌ WebSocket错误: ${error}`);
      setIsConnected(false);
    };
  };

  const sendReadyMessage = (isReady: boolean) => {
    if (!ws.current || ws.current.readyState !== WebSocket.OPEN) {
      addLog('❌ WebSocket未连接，无法发送消息');
      return;
    }

    const message = {
      action: 'set_ready',
      payload: { is_ready: isReady }
    };

    addLog(`📤 发送准备消息: ${JSON.stringify(message)}`);
    ws.current.send(JSON.stringify(message));
  };

  const getCurrentUser = () => {
    return members.find(m => m.username === user?.username);
  };

  const disconnect = () => {
    if (ws.current) {
      ws.current.close();
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  useEffect(() => {
    return () => {
      if (ws.current) {
        ws.current.close();
      }
    };
  }, []);

  const currentUser = getCurrentUser();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>准备功能调试</Text>
      
      <View style={styles.infoSection}>
        <Text style={styles.infoText}>用户: {user?.username || '未登录'}</Text>
        <Text style={styles.infoText}>房间: {roomCode}</Text>
        <Text style={styles.infoText}>连接状态: {isConnected ? '✅ 已连接' : '❌ 未连接'}</Text>
        <Text style={styles.infoText}>当前用户准备状态: {currentUser ? (currentUser.is_ready ? '✅ 已准备' : '❌ 未准备') : '未找到'}</Text>
      </View>

      <View style={styles.buttonSection}>
        <Button title="连接WebSocket" onPress={connectWebSocket} />
        <Button title="断开连接" onPress={disconnect} />
        <Button 
          title="设置准备" 
          onPress={() => sendReadyMessage(true)}
          disabled={!isConnected}
        />
        <Button 
          title="取消准备" 
          onPress={() => sendReadyMessage(false)}
          disabled={!isConnected}
        />
        <Button title="清除日志" onPress={clearLogs} />
      </View>

      <View style={styles.membersSection}>
        <Text style={styles.sectionTitle}>成员列表:</Text>
        {members.map((member, index) => (
          <Text key={index} style={styles.memberText}>
            {member.username}: {member.is_ready ? '✅ 已准备' : '❌ 未准备'}
          </Text>
        ))}
      </View>

      <View style={styles.logsSection}>
        <Text style={styles.sectionTitle}>调试日志:</Text>
        <ScrollView style={styles.logsContainer}>
          {logs.map((log, index) => (
            <Text key={index} style={styles.logText}>
              {log}
            </Text>
          ))}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  infoSection: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  infoText: {
    fontSize: 16,
    marginBottom: 5,
  },
  buttonSection: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  membersSection: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  memberText: {
    fontSize: 16,
    marginBottom: 5,
  },
  logsSection: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
  },
  logsContainer: {
    flex: 1,
  },
  logText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 2,
  },
});
