import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, Text, StyleSheet, SafeAreaView, Alert, ActivityIndicator } from 'react-native';
import { useRoute, RouteProp, useFocusEffect } from '@react-navigation/native';
import { useAuth } from '../auth/AuthContext';
import { WEBSOCKET_URL_BASE } from '../api/client';
import { RootStackParamList, EventStep, Message, PictionaryState, Member, PathData } from '../types';

import { PictionaryView } from '../components/steps/PictionaryView';
import { ChatView } from '../components/steps/ChatView';
import { LobbyView } from '../components/steps/LobbyView';
import { PathData as PictionaryCanvasPathData } from '../components/PictionaryCanvas'; // 确保类型兼容

type RoomScreenRouteProp = RouteProp<RootStackParamList, 'Room'>;

export const RoomScreen = () => {
    const route = useRoute<RoomScreenRouteProp>();
    const { room: initialRoom } = route.params;
    const { user, token } = useAuth();

    // --- 状态管理 ---
    const [currentStep, setCurrentStep] = useState<EventStep | null>(null);
    const [_stepPayload, setStepPayload] = useState<any>(null);
    const [roomStatus, setRoomStatus] = useState(initialRoom.status);
    const [isWsConnected, setIsWsConnected] = useState(false);
    const [reconnectAttempts, setReconnectAttempts] = useState(0);
    const maxReconnectAttempts = 5;
    
    // 【新增】管理房间成员列表和房主ID
    const [members, setMembers] = useState<Member[]>([]);
    const [hostId, setHostId] = useState(initialRoom.host.id);

    // 游戏相关的状态
    const [messages, setMessages] = useState<Message[]>([]);
    const [paths, setPaths] = useState<PathData[]>([]);
    const [pictionaryState, setPictionaryState] = useState<PictionaryState | null>(null);
    const localPathIds = useRef<Set<string>>(new Set());
    const drawingThrottle = useRef<{
        lastSentTime: number;
        pendingPath: PathData | null;
        timeoutId: NodeJS.Timeout | null;
    }>({
        lastSentTime: 0,
        pendingPath: null,
        timeoutId: null
    });
    
    const ws = useRef<WebSocket | null>(null);

    const connectWebSocket = useCallback(() => {
        if (!initialRoom?.room_code || !token) return;

        const wsUrl = `${WEBSOCKET_URL_BASE}/ws/room/${initialRoom.room_code}/?token=${token}`;
        if (ws.current && ws.current.readyState !== WebSocket.CLOSED) {
            ws.current.close(1000, "Reconnecting");
        }
        ws.current = new WebSocket(wsUrl);

        ws.current.onopen = () => {
            console.log('WebSocket connected');
            setIsWsConnected(true);
            setReconnectAttempts(0);
        };

        ws.current.onclose = (event) => {
            console.log('WebSocket disconnected:', event.code, event.reason);
            setIsWsConnected(false);
            if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
                const timeout = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
                setTimeout(() => {
                    setReconnectAttempts(prev => prev + 1);
                    connectWebSocket();
                }, timeout);
            } else if (reconnectAttempts >= maxReconnectAttempts) {
                Alert.alert('连接失败', '无法连接到服务器，请检查网络连接后重新进入房间。');
            }
        };

        ws.current.onerror = (e: any) => {
            console.error('WebSocket error:', e.message);
        };

        ws.current.onmessage = (e: any) => {
            try {
                const data = JSON.parse(e.data);
                switch (data.type) {
                    case 'room_state_update':
                        setMembers(data.payload.members || []);
                        setRoomStatus(data.payload.room_status);
                        setHostId(data.payload.host_id);
                        break;
                    case 'step_started':
                        setCurrentStep(data.payload.step_info);
                        setStepPayload(data.payload);
                        setRoomStatus(data.payload.room_status);
                        setMembers(data.payload.members || []);
                        setHostId(data.payload.host_id);
                        setMessages([]);
                        setPaths([]);
                        localPathIds.current.clear();
                        if (data.payload.step_info.step_type === 'GAME_PICTIONARY') {
                            setPictionaryState(data.payload);
                        }
                        break;
                    case 'round_over':
                        setCurrentStep(null);
                        setStepPayload(null);
                        setRoomStatus('WAITING');
                        setPictionaryState(null);
                        setPaths([]);
                        localPathIds.current.clear();
                        const alertTitle = data.payload.timeout ? "时间到!" : data.payload.winner ? "猜对了!" : "游戏结束!";
                        const alertMessage = data.payload.timeout ? `时间到! 正确答案是: ${data.payload.word}` : data.payload.winner ? `${data.payload.winner} 猜中了答案: ${data.payload.word}` : `正确答案是: ${data.payload.word}`;
                        if (user?.id === hostId) {
                            Alert.alert(alertTitle, alertMessage + "\n\n是否再开始一轮？", [{ text: "结束" }, { text: "再来一轮", onPress: () => sendWebSocketMessage('restart_game', { game_type: 'PICTIONARY' }) }]);
                        } else { Alert.alert(alertTitle, alertMessage); }
                        break;
                    case 'event_finished':
                        Alert.alert("活动结束", data.payload.message);
                        setRoomStatus('FINISHED');
                        setCurrentStep(null);
                        break;
                    case 'error':
                        Alert.alert("操作失败", data.payload.message);
                        break;
                    case 'chat_message':
                        setMessages(prev => [data.payload, ...prev]);
                        break;
                    case 'drawing_data':
                        const incomingPath = data.payload.path_data;
                        if (!localPathIds.current.has(incomingPath.id)) {
                            setPaths(prev => [...prev, incomingPath]);
                        }
                        break;
                    case 'step_timeout':
                        setCurrentStep(null);
                        setStepPayload(null);
                        setRoomStatus(data.payload.room_status);
                        setPictionaryState(null);
                        setPaths([]);
                        localPathIds.current.clear();
                        Alert.alert("时间到!", "环节时间已结束");
                        break;
                    default:
                        console.warn('Unknown message type:', data.type);
                }
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };
    }, [initialRoom?.room_code, token, reconnectAttempts]);

    useFocusEffect(
        useCallback(() => {
            connectWebSocket();
            return () => {
                if (ws.current) {
                    ws.current.close(1000, 'Component unmounting');
                }
                if (drawingThrottle.current.timeoutId) {
                    clearTimeout(drawingThrottle.current.timeoutId);
                }
            };
        }, [connectWebSocket])
    );

    const sendWebSocketMessage = (action: string, payload: object = {}) => {
        if (ws.current?.readyState === WebSocket.OPEN) {
            ws.current.send(JSON.stringify({ action, payload }));
        }
    };
    
    const handleReadyToggle = () => {
        const currentUserMember = members.find(m => m.id === user?.id);
        if (currentUserMember) {
            sendWebSocketMessage('set_ready', { is_ready: !currentUserMember.is_ready });
        }
    };

    const handleStartGame = () => {
        sendWebSocketMessage('start_game');
    };

    const handleDraw = (pathData: PictionaryCanvasPathData) => {
        localPathIds.current.add(pathData.id);
        setPaths(prev => [...prev, pathData]);
        const now = Date.now();
        const THROTTLE_MS = 50;
        if (now - drawingThrottle.current.lastSentTime >= THROTTLE_MS) {
            drawingThrottle.current.lastSentTime = now;
            sendWebSocketMessage('send_drawing', { path_data: pathData });
        } else {
            drawingThrottle.current.pendingPath = pathData;
            if (drawingThrottle.current.timeoutId) { clearTimeout(drawingThrottle.current.timeoutId); }
            const remainingTime = THROTTLE_MS - (now - drawingThrottle.current.lastSentTime);
            drawingThrottle.current.timeoutId = setTimeout(() => {
                if (drawingThrottle.current.pendingPath) {
                    drawingThrottle.current.lastSentTime = Date.now();
                    sendWebSocketMessage('send_drawing', { path_data: drawingThrottle.current.pendingPath });
                    drawingThrottle.current.pendingPath = null;
                }
                drawingThrottle.current.timeoutId = null;
            }, remainingTime);
        }
    };

    const handleSendMessage = (message: string) => {
        sendWebSocketMessage('send_message', { message });
    };

    const renderCurrentStep = () => {
        if (!isWsConnected && roomStatus !== 'FINISHED') {
            return (
                <View style={styles.fullScreenLoader}>
                    <ActivityIndicator size="large" color="#007bff" />
                    <Text style={styles.loaderText}>连接中...</Text>
                </View>
            );
        }

        if (roomStatus === 'WAITING' || !currentStep) {
            const isHost = user?.id === hostId;
            const currentUserMember = members.find(m => m.id === user?.id);
            const membersWithHostFlag = members.map(m => ({ ...m, is_host: m.id === hostId }));

            return (
                <LobbyView
                    members={membersWithHostFlag}
                    isHost={isHost}
                    isCurrentUserReady={currentUserMember?.is_ready || false}
                    onReadyToggle={handleReadyToggle}
                    onStartGame={handleStartGame}
                    isConnected={isWsConnected}
                    isFinished={roomStatus === 'FINISHED'}
                />
            );
        }

        switch (currentStep.step_type) {
            case 'GAME_PICTIONARY':
                if (!pictionaryState) return <ActivityIndicator size="large" />;
                return (
                    <PictionaryView
                        isDrawer={user?.username === pictionaryState.drawer}
                        pictionaryState={pictionaryState}
                        paths={paths}
                        messages={messages}
                        onDraw={handleDraw}
                        onSendMessage={handleSendMessage}
                    />
                );
            case 'FREE_CHAT':
                return (
                    <ChatView
                        messages={messages}
                        onSendMessage={handleSendMessage}
                        roomHost={initialRoom.host}
                        onNextStep={() => sendWebSocketMessage('next_step')}
                    />
                );
            default:
                const isHostDefault = user?.id === hostId;
                return <LobbyView isHost={isHostDefault} isConnected={isWsConnected} members={members} isCurrentUserReady={false} onReadyToggle={handleReadyToggle} onStartGame={handleStartGame} />;
        }
    };

    return (
        <SafeAreaView style={styles.safeArea}>
            <View style={styles.container}>
                <Text style={styles.title}>房间代码: {initialRoom.room_code}</Text>
                <View style={styles.content}>
                    {renderCurrentStep()}
                </View>
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    safeArea: { flex: 1, backgroundColor: '#f5f5f5' },
    container: { flex: 1 },
    content: { flex: 1, alignItems: 'center', justifyContent: 'center' },
    title: { fontSize: 24, fontWeight: 'bold', padding: 20, textAlign: 'center', backgroundColor: '#fff' },
    fullScreenLoader: { flex: 1, alignItems: 'center', justifyContent: 'center' },
    loaderText: { marginTop: 10, fontSize: 16, color: 'gray' },
});
