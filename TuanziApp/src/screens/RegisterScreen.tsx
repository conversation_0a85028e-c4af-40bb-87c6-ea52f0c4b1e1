import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet, Alert } from 'react-native';
import { useAuth } from '../auth/AuthContext';
import { API_URL } from '../api/client';
export const RegisterScreen = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const { login } = useAuth();

  const handleRegister = async () => {
    try {
      const response = await fetch(`${API_URL}/api/register/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password }),
      });

      if (response.status === 201) {
        Alert.alert("注册成功", "现在将为您自动登录。", [
          { text: "好的", onPress: () => login(username, password) }
        ]);
      } else {
        const data = await response.json();
        const errorMessage = Object.values(data).join('\n');
        Alert.alert("注册失败", errorMessage);
      }
    } catch (error) {
      console.error('Registration error:', error);
      Alert.alert("错误", "注册过程中发生错误。");
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>注册团子账户</Text>
      <TextInput style={styles.input} placeholder="用户名" value={username} onChangeText={setUsername} autoCapitalize="none" />
      <TextInput style={styles.input} placeholder="密码" value={password} onChangeText={setPassword} secureTextEntry />
      <Button title="注册" onPress={handleRegister} />
    </View>
  );
};

const styles = StyleSheet.create({
    container: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 },
    title: { fontSize: 24, fontWeight: 'bold', marginBottom: 20 },
    input: { width: '100%', height: 40, borderColor: 'gray', borderWidth: 1, borderRadius: 5, marginBottom: 12, paddingHorizontal: 10 },
});
