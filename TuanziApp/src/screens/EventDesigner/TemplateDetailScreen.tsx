import React, { useState, useCallback } from 'react';
import { View, Text, Button, StyleSheet, Alert, ActivityIndicator, TouchableOpacity, FlatList } from 'react-native';
import { useRoute, RouteProp, useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../../auth/AuthContext';
import { getTemplateDetails, reorderSteps } from '../../api/eventApi';
import { EventTemplate, RootStackParamList, EventStep } from '../../types';
import { commonStyles } from '../../styles/commonStyles';

type TemplateDetailRouteProp = RouteProp<RootStackParamList, 'TemplateDetail'>;
type NavigationProp = StackNavigationProp<RootStackParamList, 'TemplateDetail'>;

export const TemplateDetailScreen = () => {
    const route = useRoute<TemplateDetailRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const { templateId } = route.params;
    const { token } = useAuth();

    const [template, setTemplate] = useState<EventTemplate | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [steps, setSteps] = useState<EventStep[]>([]);

    const fetchDetails = useCallback(async () => {
        if (!token) return;
        try {
            setIsLoading(true);
            const data = await getTemplateDetails(token, templateId);
            setTemplate(data);
            setSteps(data.steps);
        } catch (error) {
            Alert.alert('错误', '无法加载模板详情。');
        } finally {
            setIsLoading(false);
        }
    }, [token, templateId]);

    useFocusEffect(useCallback(() => { fetchDetails(); }, [fetchDetails]));

    const moveStep = async (stepIndex: number, direction: 'up' | 'down') => {
        if (!token) return;

        const newSteps = [...steps];
        const targetIndex = direction === 'up' ? stepIndex - 1 : stepIndex + 1;

        // Check bounds
        if (targetIndex < 0 || targetIndex >= newSteps.length) return;

        // Swap steps
        [newSteps[stepIndex], newSteps[targetIndex]] = [newSteps[targetIndex], newSteps[stepIndex]];

        try {
            // Update local state immediately
            setSteps(newSteps);

            // Extract step IDs in new order
            const stepIds = newSteps.map(step => step.id);

            // Call API to update order on backend
            await reorderSteps(token, templateId, stepIds);

            // Refresh to ensure consistency
            await fetchDetails();
        } catch (error) {
            console.error('Failed to reorder steps:', error);
            Alert.alert('错误', '重新排序失败，请重试。');
            // Revert on error
            await fetchDetails();
        }
    };

    const getStepDisplayName = (step: EventStep) => {
        if (step.name && step.name.trim()) {
            return step.name;
        }
        // Fallback to step type display name
        switch (step.step_type) {
            case 'GAME_PICTIONARY':
                return '游戏：你画我猜';
            case 'FREE_CHAT':
                return '自由讨论';
            default:
                return step.step_type.replace(/_/g, ' ');
        }
    };

    const getStepTypeLabel = (stepType: string) => {
        switch (stepType) {
            case 'GAME_PICTIONARY':
                return '你画我猜';
            case 'FREE_CHAT':
                return '自由讨论';
            default:
                return stepType.replace(/_/g, ' ');
        }
    };

    const renderStepItem = ({ item, index }: { item: EventStep; index: number }) => (
        <View style={styles.stepCard}>
            <TouchableOpacity
                style={styles.stepMainContent}
                onPress={() => navigation.navigate('EditStep', { stepId: item.id })}
            >
                <View style={styles.stepContent}>
                    <View style={styles.stepHeader}>
                        <View style={styles.stepOrderBadge}>
                            <Text style={styles.stepOrderText}>{item.order}</Text>
                        </View>
                        <View style={styles.stepInfo}>
                            <Text style={styles.stepName}>{getStepDisplayName(item)}</Text>
                            <Text style={styles.stepType}>{getStepTypeLabel(item.step_type)}</Text>
                        </View>
                        <Text style={styles.stepDuration}>{item.duration / 60}分钟</Text>
                    </View>
                </View>
                <Text style={styles.editHint}>点击编辑</Text>
            </TouchableOpacity>

            <View style={styles.stepControls}>
                <TouchableOpacity
                    style={[styles.moveButton, index === 0 && styles.disabledButton]}
                    onPress={() => moveStep(index, 'up')}
                    disabled={index === 0}
                >
                    <Text style={[styles.moveButtonText, index === 0 && styles.disabledText]}>↑</Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[styles.moveButton, index === steps.length - 1 && styles.disabledButton]}
                    onPress={() => moveStep(index, 'down')}
                    disabled={index === steps.length - 1}
                >
                    <Text style={[styles.moveButtonText, index === steps.length - 1 && styles.disabledText]}>↓</Text>
                </TouchableOpacity>
            </View>
        </View>
    );

    if (isLoading) {
        return <View style={commonStyles.container}><ActivityIndicator size="large" /></View>;
    }

    if (!template) {
        return <View style={commonStyles.container}><Text>未找到模板。</Text></View>;
    }

    return (
        <View style={styles.container}>
            <FlatList
                data={steps}
                renderItem={({ item, index }) => renderStepItem({ item, index })}
                keyExtractor={(item) => item.id.toString()}
                ListHeaderComponent={
                    <View style={styles.header}>
                        <Text style={commonStyles.title}>{template.name}</Text>
                        <Text style={styles.description}>{template.description}</Text>
                    </View>
                }
                ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                        <Text style={styles.emptyIcon}>📝</Text>
                        <Text style={styles.emptyTitle}>还没有步骤</Text>
                        <Text style={styles.emptyText}>点击下方按钮添加第一个环节步骤</Text>
                    </View>
                }
                contentContainerStyle={styles.listContainer}
            />

            <View style={styles.buttonContainer}>
                <Button title="添加新步骤" onPress={() => navigation.navigate('AddStep', { templateId })} />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1, backgroundColor: '#f5f5f5' },
    header: { padding: 20, backgroundColor: '#fff', borderBottomWidth: 1, borderColor: '#eee' },
    description: { fontSize: 16, color: '#666', marginTop: 10, textAlign: 'center' },
    listContainer: { paddingVertical: 10 },
    buttonContainer: { padding: 20, backgroundColor: '#fff' },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 40,
        paddingVertical: 60,
    },
    emptyIcon: {
        fontSize: 48,
        marginBottom: 16,
    },
    emptyTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
        marginBottom: 8,
        textAlign: 'center',
    },
    stepCard: {
        backgroundColor: '#fff',
        marginVertical: 6,
        marginHorizontal: 16,
        borderRadius: 12,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        flexDirection: 'row',
    },
    stepMainContent: {
        flex: 1,
        padding: 15,
    },
    activeStepCard: {
        elevation: 8,
        shadowOpacity: 0.3,
        transform: [{ scale: 1.02 }],
    },
    stepContent: {
        marginBottom: 8,
    },
    stepHeader: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    stepOrderBadge: {
        width: 28,
        height: 28,
        borderRadius: 14,
        backgroundColor: '#007AFF',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    stepOrderText: {
        color: '#fff',
        fontSize: 12,
        fontWeight: '600',
    },
    stepInfo: {
        flex: 1,
    },
    stepName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 2,
    },
    stepType: {
        fontSize: 12,
        color: '#666',
        fontStyle: 'italic',
    },
    stepDuration: {
        fontSize: 14,
        color: '#007AFF',
        fontWeight: '500',
    },
    editHint: { fontSize: 12, color: '#999', textAlign: 'center', fontStyle: 'italic' },
    stepControls: {
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 15,
    },
    moveButton: {
        backgroundColor: '#f0f0f0',
        width: 32,
        height: 32,
        borderRadius: 16,
        justifyContent: 'center',
        alignItems: 'center',
        marginVertical: 2,
    },
    moveButtonText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#007AFF',
    },
    disabledButton: {
        backgroundColor: '#e0e0e0',
    },
    disabledText: {
        color: '#ccc',
    },
    emptyText: {
        textAlign: 'center',
        fontSize: 14,
        color: '#888',
        lineHeight: 20,
    },
});
