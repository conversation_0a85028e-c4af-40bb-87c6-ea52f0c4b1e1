import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, Button, StyleSheet, Alert, ActivityIndicator, TouchableOpacity, ScrollView } from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../../auth/AuthContext';
import { getStepDetails, updateStep, deleteStep } from '../../api/eventApi';
import { RootStackParamList, EventStep } from '../../types';
import { commonStyles } from '../../styles/commonStyles';

type EditStepRouteProp = RouteProp<RootStackParamList, 'EditStep'>;
type NavigationProp = StackNavigationProp<RootStackParamList, 'EditStep'>;

const STEP_CHOICES = [
    { label: '游戏：你画我猜', value: 'GAME_PICTIONARY' },
    { label: '自由讨论', value: 'FREE_CHAT' },
];

export const EditStepScreen = () => {
    const route = useRoute<EditStepRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const { stepId } = route.params;
    const { token } = useAuth();
    
    const [step, setStep] = useState<EventStep | null>(null);
    const [name, setName] = useState('');
    const [stepType, setStepType] = useState('');
    const [duration, setDuration] = useState('');
    const [isLoading, setIsLoading] = useState(true);
    const [isSaving, setIsSaving] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);

    useEffect(() => {
        const fetchStepDetails = async () => {
            if (!token) return;
            
            try {
                setIsLoading(true);
                const stepData = await getStepDetails(token, stepId);
                setStep(stepData);
                setName(stepData.name || '');
                setStepType(stepData.step_type);
                setDuration((stepData.duration / 60).toString()); // Convert seconds to minutes
            } catch (error) {
                console.error('Failed to fetch step details:', error);
                Alert.alert('错误', '无法加载步骤详情。');
                navigation.goBack();
            } finally {
                setIsLoading(false);
            }
        };

        fetchStepDetails();
    }, [token, stepId, navigation]);

    const handleSave = async () => {
        if (!token || !step) return;

        // Validation
        if (!stepType) {
            Alert.alert('错误', '请选择一个环节类型。');
            return;
        }

        const durationNum = parseInt(duration);
        if (isNaN(durationNum) || durationNum <= 0) {
            Alert.alert('错误', '请输入有效的时长（分钟）。');
            return;
        }

        setIsSaving(true);
        try {
            const updateData = {
                name: name.trim(),
                step_type: stepType,
                duration: durationNum * 60, // Convert minutes to seconds
            };

            await updateStep(token, stepId, updateData);
            Alert.alert('成功', '步骤已更新！', [
                { text: '确定', onPress: () => navigation.goBack() }
            ]);
        } catch (error) {
            console.error('Failed to update step:', error);
            Alert.alert('错误', '更新步骤失败。');
        } finally {
            setIsSaving(false);
        }
    };

    const handleDelete = async () => {
        if (!token || !step) return;

        Alert.alert(
            '确认删除',
            '确定要删除这个步骤吗？此操作无法撤销。',
            [
                { text: '取消', style: 'cancel' },
                {
                    text: '删除',
                    style: 'destructive',
                    onPress: async () => {
                        setIsDeleting(true);
                        try {
                            await deleteStep(token, stepId);
                            Alert.alert('成功', '步骤已删除！', [
                                { text: '确定', onPress: () => navigation.goBack() }
                            ]);
                        } catch (error) {
                            console.error('Failed to delete step:', error);
                            Alert.alert('错误', '删除步骤失败。');
                        } finally {
                            setIsDeleting(false);
                        }
                    }
                }
            ]
        );
    };

    const getStepTypeLabel = (value: string) => {
        const choice = STEP_CHOICES.find(c => c.value === value);
        return choice ? choice.label : value;
    };

    if (isLoading) {
        return (
            <View style={commonStyles.container}>
                <ActivityIndicator size="large" />
                <Text style={styles.loadingText}>加载中...</Text>
            </View>
        );
    }

    if (!step) {
        return (
            <View style={commonStyles.container}>
                <Text>未找到步骤。</Text>
            </View>
        );
    }

    return (
        <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
            <Text style={commonStyles.title}>编辑步骤</Text>
            
            <View style={styles.section}>
                <Text style={commonStyles.subtitle}>步骤名称（可选）</Text>
                <TextInput
                    style={commonStyles.input}
                    placeholder="例如：开场破冰游戏"
                    value={name}
                    onChangeText={setName}
                />
                <Text style={styles.hint}>留空将使用默认名称</Text>
            </View>

            <View style={styles.section}>
                <Text style={commonStyles.subtitle}>环节类型</Text>
                {STEP_CHOICES.map(choice => (
                    <TouchableOpacity
                        key={choice.value}
                        style={[
                            styles.choiceButton,
                            stepType === choice.value && styles.selectedChoice
                        ]}
                        onPress={() => setStepType(choice.value)}
                    >
                        <Text style={[
                            styles.choiceText,
                            stepType === choice.value && styles.selectedChoiceText
                        ]}>
                            {choice.label}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>

            <View style={styles.section}>
                <Text style={commonStyles.subtitle}>时长（分钟）</Text>
                <TextInput
                    style={commonStyles.input}
                    placeholder="5"
                    value={duration}
                    onChangeText={setDuration}
                    keyboardType="numeric"
                />
            </View>

            <View style={styles.buttonContainer}>
                {isSaving ? (
                    <ActivityIndicator size="large" />
                ) : (
                    <Button title="保存更改" onPress={handleSave} />
                )}
            </View>

            <View style={styles.deleteButtonContainer}>
                {isDeleting ? (
                    <ActivityIndicator size="large" color="#ff4444" />
                ) : (
                    <Button title="删除步骤" onPress={handleDelete} color="#ff4444" />
                )}
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    contentContainer: {
        padding: 20,
    },
    loadingText: {
        textAlign: 'center',
        marginTop: 10,
        fontSize: 16,
        color: '#666',
    },
    section: {
        marginBottom: 25,
    },
    hint: {
        fontSize: 12,
        color: '#888',
        marginTop: 5,
        fontStyle: 'italic',
    },
    choiceButton: {
        width: '100%',
        padding: 15,
        marginVertical: 8,
        backgroundColor: '#fff',
        borderRadius: 10,
        borderWidth: 2,
        borderColor: '#e0e0e0',
    },
    selectedChoice: {
        borderColor: '#007AFF',
        backgroundColor: '#e6f2ff',
    },
    choiceText: {
        fontSize: 16,
        textAlign: 'center',
        color: '#333',
    },
    selectedChoiceText: {
        color: '#007AFF',
        fontWeight: '600',
    },
    buttonContainer: {
        marginTop: 30,
        marginBottom: 20,
    },
    deleteButtonContainer: {
        marginBottom: 40,
    },
});
