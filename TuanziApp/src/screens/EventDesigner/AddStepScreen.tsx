import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet, Alert, ActivityIndicator, TouchableOpacity, ScrollView } from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../../auth/AuthContext';
import { addStepToTemplate } from '../../api/eventApi';
import { RootStackParamList } from '../../types';
import { commonStyles } from '../../styles/commonStyles';

type AddStepRouteProp = RouteProp<RootStackParamList, 'AddStep'>;
type NavigationProp = StackNavigationProp<RootStackParamList, 'AddStep'>;

const STEP_CHOICES = [
    { label: '游戏：你画我猜', value: 'GAME_PICTIONARY' },
    { label: '自由讨论', value: 'FREE_CHAT' },
];

export const AddStepScreen = () => {
    const route = useRoute<AddStepRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const { templateId } = route.params;
    const { token } = useAuth();
    
    const [name, setName] = useState('');
    const [selectedStep, setSelectedStep] = useState<string | null>(null);
    const [duration, setDuration] = useState('5');
    const [isLoading, setIsLoading] = useState(false);

    const handleSave = async () => {
        if (!selectedStep) {
            Alert.alert('错误', '请选择一个环节类型。');
            return;
        }

        const durationNum = parseInt(duration);
        if (isNaN(durationNum) || durationNum <= 0) {
            Alert.alert('错误', '请输入有效的时长（分钟）。');
            return;
        }

        if (!token) return;

        setIsLoading(true);
        try {
            await addStepToTemplate(token, templateId, {
                name: name.trim(),
                step_type: selectedStep,
                duration: durationNum * 60, // Convert minutes to seconds
            });
            Alert.alert('成功', '新步骤已添加！');
            navigation.goBack();
        } catch (error) {
            console.error(error);
            Alert.alert('错误', '添加步骤失败。');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
            <Text style={commonStyles.title}>添加新步骤</Text>

            <View style={styles.section}>
                <Text style={commonStyles.subtitle}>步骤名称（可选）</Text>
                <TextInput
                    style={commonStyles.input}
                    placeholder="例如：开场破冰游戏"
                    value={name}
                    onChangeText={setName}
                />
                <Text style={styles.hint}>留空将使用默认名称</Text>
            </View>

            <View style={styles.section}>
                <Text style={commonStyles.subtitle}>环节类型</Text>
                {STEP_CHOICES.map(choice => (
                    <TouchableOpacity
                        key={choice.value}
                        style={[
                            styles.choiceButton,
                            selectedStep === choice.value && styles.selectedChoice
                        ]}
                        onPress={() => setSelectedStep(choice.value)}
                    >
                        <Text style={[
                            styles.choiceText,
                            selectedStep === choice.value && styles.selectedChoiceText
                        ]}>
                            {choice.label}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>

            <View style={styles.section}>
                <Text style={commonStyles.subtitle}>时长（分钟）</Text>
                <TextInput
                    style={commonStyles.input}
                    placeholder="5"
                    value={duration}
                    onChangeText={setDuration}
                    keyboardType="numeric"
                />
            </View>

            <View style={styles.buttonContainer}>
                {isLoading ? (
                    <ActivityIndicator size="large" />
                ) : (
                    <Button title="确认添加" onPress={handleSave} disabled={!selectedStep} />
                )}
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    contentContainer: {
        padding: 20,
    },
    section: {
        marginBottom: 25,
    },
    hint: {
        fontSize: 12,
        color: '#888',
        marginTop: 5,
        fontStyle: 'italic',
    },
    choiceButton: {
        width: '100%',
        padding: 15,
        marginVertical: 8,
        backgroundColor: '#fff',
        borderRadius: 10,
        borderWidth: 2,
        borderColor: '#e0e0e0',
    },
    selectedChoice: {
        borderColor: '#007AFF',
        backgroundColor: '#e6f2ff',
    },
    choiceText: {
        fontSize: 16,
        textAlign: 'center',
        color: '#333',
    },
    selectedChoiceText: {
        color: '#007AFF',
        fontWeight: '600',
    },
    buttonContainer: {
        marginTop: 30,
        marginBottom: 40,
    },
});
