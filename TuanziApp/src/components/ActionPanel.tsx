import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, Animated } from 'react-native';
import { useAuth } from '../auth/AuthContext';

interface ActionPanelProps {
  roomHost: string; // 保留用于向后兼容，但优先使用isCurrentUserHost
  isCurrentUserHost?: boolean; // 【新增】直接传递当前用户是否为房主
  isVisible: boolean;
  onClose: () => void;
  onNextStep?: () => void;
  // 未来可以添加更多操作
  // onKickPlayer?: (playerId: string) => void;
  // onChangeSettings?: () => void;
}

export const ActionPanel: React.FC<ActionPanelProps> = ({
  roomHost,
  isCurrentUserHost,
  isVisible,
  onClose,
  onNextStep,
}) => {
  const { user } = useAuth();
  const [slideAnim] = useState(new Animated.Value(300)); // 初始位置在屏幕外

  React.useEffect(() => {
    if (isVisible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 300,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible, slideAnim]);

  // 权限检查 - 优先使用传入的isCurrentUserHost，否则回退到username比较
  const isHost = isCurrentUserHost !== undefined ? isCurrentUserHost : (user?.username === roomHost);
  // 未来可以添加管理员权限检查
  // const isAdmin = user?.role === 'admin';
  const hasAdminPrivileges = isHost; // 目前只有房主有管理权限

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableOpacity 
        style={styles.overlay} 
        activeOpacity={1} 
        onPress={onClose}
      >
        <Animated.View 
          style={[
            styles.panel,
            {
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <TouchableOpacity activeOpacity={1}>
            <View style={styles.header}>
              <Text style={styles.headerText}>操作面板</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Text style={styles.closeButtonText}>×</Text>
              </TouchableOpacity>
            </View>

            {/* 所有用户都可以进行的操作 */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>通用操作</Text>
              <TouchableOpacity style={styles.actionButton}>
                <Text style={styles.actionButtonText}>查看房间信息</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Text style={styles.actionButtonText}>查看参与者列表</Text>
              </TouchableOpacity>
            </View>

            {/* 只有房主/管理员可以进行的操作 */}
            {hasAdminPrivileges && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>管理操作</Text>
                {onNextStep && (
                  <TouchableOpacity 
                    style={[styles.actionButton, styles.primaryButton]} 
                    onPress={() => {
                      onNextStep();
                      onClose();
                    }}
                  >
                    <Text style={[styles.actionButtonText, styles.primaryButtonText]}>
                      进入下一环节
                    </Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity style={styles.actionButton}>
                  <Text style={styles.actionButtonText}>房间设置</Text>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.actionButton, styles.dangerButton]}>
                  <Text style={[styles.actionButtonText, styles.dangerButtonText]}>
                    结束活动
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            {/* 显示用户权限信息 */}
            <View style={styles.footer}>
              <Text style={styles.footerText}>
                当前身份: {isHost ? '房主' : '参与者'}
              </Text>
            </View>
          </TouchableOpacity>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  panel: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '70%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 20,
    color: '#666',
  },
  section: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  actionButton: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  actionButtonText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
  },
  primaryButton: {
    backgroundColor: '#007bff',
    borderColor: '#007bff',
  },
  primaryButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  dangerButton: {
    backgroundColor: '#dc3545',
    borderColor: '#dc3545',
  },
  dangerButtonText: {
    color: 'white',
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#666',
  },
});
