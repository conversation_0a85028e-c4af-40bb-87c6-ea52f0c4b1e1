import React, { useState, useRef, useCallback, useMemo } from 'react';
import { View, StyleSheet, PanResponder } from 'react-native';
import Svg, { Path } from 'react-native-svg';

// Generate unique ID for paths
let pathIdCounter = 0;
const generateUniquePathId = () => {
  pathIdCounter += 1;
  return `path_${Date.now()}_${pathIdCounter}_${Math.random().toString(36).substr(2, 9)}`;
};

// Type definition for a single continuous line drawn by the user
export interface PathData {
  id: string;
  path: string;
  color: string;
}

// Props interface for the PictionaryCanvas component
interface PictionaryCanvasProps {
  isDrawer: boolean;
  onDraw: (pathData: PathData) => void;
  paths: PathData[];
}

export const PictionaryCanvas: React.FC<PictionaryCanvasProps> = ({
  isDrawer,
  onDraw,
  paths,
}) => {
  // State to hold the path currently being drawn in real-time
  const [currentPath, setCurrentPath] = useState<PathData | null>(null);
  // A ref to the canvas's container View to measure its position
  const viewRef = useRef<View>(null);
  // Throttle drawing updates to improve performance
  const lastDrawTime = useRef<number>(0);
  const DRAW_THROTTLE_MS = 8; // ~120fps for smoother drawing
  const pendingUpdate = useRef<boolean>(false);

  // 缓存画布位置信息以减少重复测量
  const canvasPosition = useRef<{pageX: number, pageY: number} | null>(null);

  // Helper function to update path safely with optimizations
  const updatePath = useCallback((gestureState: any) => {
    if (!currentPath) return;

    const updatePathPosition = (pageX: number, pageY: number) => {
      // Calculate the new point's position within the canvas
      const adjustedX = Math.round(gestureState.moveX - pageX);
      const adjustedY = Math.round(gestureState.moveY - pageY);

      // 距离检查：只有当移动距离足够大时才添加新点（减少路径复杂度）
      const lastPoint = currentPath.path.split(' ').pop();
      if (lastPoint && lastPoint.startsWith('L')) {
        const [lastX, lastY] = lastPoint.substring(1).split(',').map(Number);
        const distance = Math.sqrt(Math.pow(adjustedX - lastX, 2) + Math.pow(adjustedY - lastY, 2));
        if (distance < 2) return; // 忽略小于2像素的移动
      }

      // 安全检查：确保 currentPath 存在且有 path 属性
      setCurrentPath(prevPath => {
        if (!prevPath || !prevPath.path) {
          console.warn('PictionaryCanvas: prevPath is null or missing path property');
          return prevPath;
        }
        return {
          ...prevPath,
          path: `${prevPath.path} L${adjustedX},${adjustedY}`,
        };
      });
    };

    // 使用缓存的位置信息或重新测量
    if (canvasPosition.current) {
      updatePathPosition(canvasPosition.current.pageX, canvasPosition.current.pageY);
    } else {
      viewRef.current?.measure((x, y, width, height, pageX, pageY) => {
        canvasPosition.current = { pageX, pageY };
        updatePathPosition(pageX, pageY);
      });
    }
  }, [currentPath]);

  // PanResponder handles all the touch gestures on the canvas
  const panResponder = useMemo(() => PanResponder.create({
    // Ask to be the responder when a touch starts
    onStartShouldSetPanResponder: () => isDrawer,
    onMoveShouldSetPanResponder: () => isDrawer,

    // When a touch gesture starts on the canvas
    onPanResponderGrant: (e, gestureState) => {
      // 重置画布位置缓存，强制重新测量
      canvasPosition.current = null;

      // Measure the absolute position (pageX, pageY) of the canvas on the screen
      viewRef.current?.measure((x, y, width, height, pageX, pageY) => {
        // 缓存画布位置
        canvasPosition.current = { pageX, pageY };

        // Calculate the precise starting point of the touch within the canvas
        const adjustedX = Math.round(gestureState.x0 - pageX);
        const adjustedY = Math.round(gestureState.y0 - pageY);

        // Start a new path at the adjusted coordinates
        setCurrentPath({
          id: generateUniquePathId(), // Generate truly unique ID for the new path
          path: `M${adjustedX},${adjustedY}`,
          color: 'black',
        });
      });
    },

    // When the finger moves across the screen
    onPanResponderMove: (e, gestureState) => {
      // Throttle drawing updates to improve performance
      const now = Date.now();
      if (now - lastDrawTime.current < DRAW_THROTTLE_MS && !pendingUpdate.current) {
        pendingUpdate.current = true;
        // Schedule the update for the next frame
        requestAnimationFrame(() => {
          pendingUpdate.current = false;
          updatePath(gestureState);
        });
        return;
      }
      lastDrawTime.current = now;
      updatePath(gestureState);
    },

    // When the touch gesture is released from the screen
    onPanResponderRelease: () => {
      // The path is complete. Send it to the parent component to be broadcasted.
      if (currentPath && currentPath.path && currentPath.path.length > 5) {
        // 只有当路径有足够内容时才发送（避免发送空路径或点击）
        onDraw(currentPath);
      }
      // Clear the current path, as it's now part of the main `paths` array
      setCurrentPath(null);
      // 清除画布位置缓存
      canvasPosition.current = null;
    },
  }), [isDrawer, currentPath, updatePath, onDraw]);

  // Combine the paths received from the server with the path currently being drawn
  const allPaths = useMemo(() => {
    return currentPath ? [...paths, currentPath] : paths;
  }, [paths, currentPath]);

  // 优化的路径渲染组件
  const PathComponent = React.memo(({ pathData }: { pathData: PathData }) => (
    <Path
      key={pathData.id}
      d={pathData.path}
      stroke={pathData.color}
      strokeWidth={3}
      fill="none"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  ));

  return (
    // Attach the ref and PanResponder handlers to the container View
    <View ref={viewRef} style={styles.container} {...panResponder.panHandlers}>
      <Svg width="100%" height="100%">
        {/* Render all paths with optimized components */}
        {allPaths.map(pathData => (
          <PathComponent key={pathData.id} pathData={pathData} />
        ))}
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
  },
});
