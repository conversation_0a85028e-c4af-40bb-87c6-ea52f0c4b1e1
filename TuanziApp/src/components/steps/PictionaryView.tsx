import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TextInput, Button, TouchableOpacity } from 'react-native';
import { PictionaryCanvas, PathData } from '../PictionaryCanvas';
import { Message } from '../../types';
import { ActionPanel } from '../ActionPanel';

// This component is now fully "dumb". It only renders what it's told to.
interface PictionaryViewProps {
  isDrawer: boolean;
  pictionaryState: { drawer: string; word: string; duration?: number; };
  paths: PathData[];
  messages: Message[];
  onDraw: (pathData: PathData) => void;
  onSendMessage: (message: string) => void; // Add message handler
  roomHost: string; // 【新增】房主信息
  isCurrentUserHost?: boolean; // 【新增】当前用户是否为房主
  onNextStep?: () => void; // 【新增】下一环节回调
}

export const PictionaryView: React.FC<PictionaryViewProps> = ({
  isDrawer,
  pictionaryState,
  paths,
  messages,
  onDraw,
  onSendMessage,
  roomHost,
  isCurrentUserHost,
  onNextStep,
}) => {
  // Chat input is now local to this component
  const [messageInput, setMessageInput] = useState('');
  // Timer state - get duration from backend or default to 60 seconds
  const [timeLeft, setTimeLeft] = useState(pictionaryState.duration || 60);
  // 【新增】ActionPanel状态
  const [showActionPanel, setShowActionPanel] = useState(false);

  // Timer effect
  useEffect(() => {
    if (timeLeft <= 0) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          // Time's up - backend will handle the timeout
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft]);

  // Reset timer when game starts
  useEffect(() => {
    setTimeLeft(pictionaryState.duration || 60);
  }, [pictionaryState]);

  const handleSendMessage = () => {
    if (messageInput.trim() && !isDrawer) {
      onSendMessage(messageInput.trim());
      setMessageInput('');
    }
  };

  return (
    <View style={styles.gameContainer}>
      <View style={styles.topInfoPanel}>
        <Text style={styles.infoText}>绘画者: {pictionaryState.drawer}</Text>
        <Text style={[styles.infoText, timeLeft <= 10 && styles.urgentTime]}>
          时间: {timeLeft}s
        </Text>
        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => setShowActionPanel(true)}
        >
          <Text style={styles.menuButtonText}>⋯</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.wordPanel}>
        <Text style={styles.wordText}>{pictionaryState.word}</Text>
        {isDrawer && <Text style={styles.roleText}>(你是绘画者)</Text>}
      </View>
      <View style={styles.mainArea}>
        <View style={styles.canvasContainer}>
          <PictionaryCanvas
            isDrawer={isDrawer}
            onDraw={onDraw}
            paths={paths}
          />
        </View>
        <View style={styles.chatContainer}>
          <FlatList
            style={styles.chatList}
            data={messages}
            renderItem={({ item }) => (
              <View style={styles.messageBubble}>
                <Text style={styles.messageSender}>{item.sender}:</Text>
                <Text style={styles.messageText}>{item.message}</Text>
              </View>
            )}
            keyExtractor={(_, index) => index.toString()}
            inverted
          />
          <View style={styles.inputArea}>
            <TextInput
              style={styles.chatInput}
              placeholder={isDrawer ? "绘画中..." : "输入你的猜测..."}
              value={messageInput}
              onChangeText={setMessageInput}
              editable={!isDrawer}
            />
            <Button title="发送" onPress={handleSendMessage} disabled={isDrawer} />
          </View>
        </View>
      </View>

      <ActionPanel
        roomHost={roomHost}
        isCurrentUserHost={isCurrentUserHost}
        isVisible={showActionPanel}
        onClose={() => setShowActionPanel(false)}
        onNextStep={onNextStep}
      />
    </View>
  );
};

const styles = StyleSheet.create({
    gameContainer: { flex: 1, width: '100%', alignItems: 'center' },
    topInfoPanel: { flexDirection: 'row', justifyContent: 'space-around', width: '100%', paddingVertical: 5, alignItems: 'center' },
    infoText: { fontSize: 16, color: '#666' },
    urgentTime: { color: '#ff4444', fontWeight: 'bold' },
    menuButton: {
        width: 30,
        height: 30,
        borderRadius: 15,
        backgroundColor: '#f0f0f0',
        justifyContent: 'center',
        alignItems: 'center',
    },
    menuButtonText: {
        fontSize: 16,
        color: '#666',
        fontWeight: 'bold',
    },
    wordPanel: { marginVertical: 5, alignItems: 'center' },
    wordText: { fontSize: 28, fontWeight: 'bold', letterSpacing: 8 },
    roleText: { fontSize: 14, color: 'gray', marginTop: 4 },
    mainArea: { flex: 1, width: '100%', flexDirection: 'row', padding: 10 },
    canvasContainer: { flex: 3, marginRight: 10 },
    chatContainer: { flex: 2, borderWidth: 1, borderColor: '#eee', borderRadius: 10, backgroundColor: '#f9f9f9' },
    chatList: { flex: 1, padding: 5 },
    inputArea: { flexDirection: 'row', padding: 5, borderTopWidth: 1, borderColor: '#eee' },
    chatInput: { flex: 1, height: 40, borderWidth: 1, borderColor: '#ccc', borderRadius: 20, paddingHorizontal: 10, marginRight: 5 },
    messageBubble: { padding: 8, marginVertical: 4 },
    messageSender: { fontWeight: 'bold' },
    messageText: { fontSize: 15 },
});
