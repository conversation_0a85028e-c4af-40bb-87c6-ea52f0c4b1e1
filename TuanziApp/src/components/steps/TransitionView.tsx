import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';

interface TransitionViewProps {
  message?: string;
}

export const TransitionView: React.FC<TransitionViewProps> = ({ 
  message = "环节结束，等待下一环节..." 
}) => {
  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color="#007bff" />
      <Text style={styles.message}>{message}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  message: {
    marginTop: 20,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});
