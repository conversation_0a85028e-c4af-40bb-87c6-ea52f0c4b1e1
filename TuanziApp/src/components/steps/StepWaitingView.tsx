import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity } from 'react-native';

interface StepWaitingViewProps {
  message?: string;
  isHost?: boolean;
  onNextStep?: () => void;
  showNextButton?: boolean;
}

export const StepWaitingView: React.FC<StepWaitingViewProps> = ({ 
  message = "环节已结束，等待下一环节...",
  isHost = false,
  onNextStep,
  showNextButton = false
}) => {
  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color="#007bff" />
      <Text style={styles.message}>{message}</Text>
      
      {isHost && showNextButton && onNextStep && (
        <TouchableOpacity style={styles.nextButton} onPress={onNextStep}>
          <Text style={styles.nextButtonText}>开始下一环节</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  message: {
    marginTop: 20,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  nextButton: {
    backgroundColor: '#007bff',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginTop: 20,
  },
  nextButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
