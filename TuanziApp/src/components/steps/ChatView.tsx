import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TextInput, Button, KeyboardAvoidingView, Platform, TouchableOpacity } from 'react-native';
import { useAuth } from '../../auth/AuthContext';
import { Message } from '../../types';
import { ActionPanel } from '../ActionPanel';

// Props that this component receives from RoomScreen
interface ChatViewProps {
  messages: Message[];
  onSendMessage: (message: string) => void;
  roomHost: string;
  isCurrentUserHost?: boolean; // 【新增】当前用户是否为房主
  onNextStep?: () => void;
}

export const ChatView: React.FC<ChatViewProps> = ({ messages, onSendMessage, roomHost, isCurrentUserHost, onNextStep }) => {
  const { user } = useAuth();
  const [messageInput, setMessageInput] = useState<string>('');
  const [showActionPanel, setShowActionPanel] = useState(false);

  const handleSendMessage = () => {
    if (messageInput.trim()) {
      onSendMessage(messageInput.trim());
      setMessageInput('');
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <View style={styles.header}>
        <Text style={styles.title}>自由讨论</Text>
        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => setShowActionPanel(true)}
        >
          <Text style={styles.menuButtonText}>⋯</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        style={styles.chatList}
        data={messages}
        renderItem={({ item }) => (
          <View style={item.sender === user?.username ? styles.myMessageBubble : styles.theirMessageBubble}>
            <Text style={styles.messageSender}>{item.sender}:</Text>
            <Text style={styles.messageText}>{item.message}</Text>
          </View>
        )}
        keyExtractor={(_, index) => index.toString()}
        inverted
      />
      <View style={styles.inputArea}>
        <TextInput
          style={styles.chatInput}
          placeholder="输入聊天消息..."
          value={messageInput}
          onChangeText={setMessageInput}
        />
        <Button title="发送" onPress={handleSendMessage} />
      </View>

      <ActionPanel
        roomHost={roomHost}
        isCurrentUserHost={isCurrentUserHost}
        isVisible={showActionPanel}
        onClose={() => setShowActionPanel(false)}
        onNextStep={onNextStep}
      />
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
    container: { flex: 1, width: '100%' },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee'
    },
    title: { fontSize: 20, fontWeight: 'bold' },
    menuButton: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#f0f0f0',
        justifyContent: 'center',
        alignItems: 'center',
    },
    menuButtonText: {
        fontSize: 20,
        color: '#666',
        fontWeight: 'bold',
    },
    chatList: { flex: 1, paddingHorizontal: 10 },
    inputArea: { flexDirection: 'row', padding: 10, borderTopWidth: 1, borderColor: '#ccc' },
    chatInput: { flex: 1, height: 40, borderWidth: 1, borderColor: 'gray', borderRadius: 20, paddingHorizontal: 15, marginRight: 10 },
    myMessageBubble: { backgroundColor: '#dcf8c6', padding: 10, borderRadius: 15, marginVertical: 4, maxWidth: '80%', alignSelf: 'flex-end', marginRight: 10 },
    theirMessageBubble: { backgroundColor: '#f0f0f0', padding: 10, borderRadius: 15, marginVertical: 4, maxWidth: '80%', alignSelf: 'flex-start', marginLeft: 10 },
    messageSender: { fontWeight: 'bold', marginBottom: 3 },
    messageText: { fontSize: 16 },
});
