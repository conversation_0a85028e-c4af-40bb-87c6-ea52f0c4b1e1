import React from 'react';
import { View, Text, StyleSheet, FlatList, ActivityIndicator, TouchableOpacity } from 'react-native';
import { Member } from '../../types';

// 定义LobbyView组件需要接收的所有“道具”(props)的类型
interface LobbyViewProps {
  members: Member[];
  isHost: boolean;
  isCurrentUserReady: boolean;
  isConnected: boolean;
  isFinished?: boolean;
  onReadyToggle: () => void;
  onStartGame: () => void;
}

export const LobbyView: React.FC<LobbyViewProps> = ({
  members,
  isHost,
  isCurrentUserReady,
  isConnected,
  isFinished,
  onReadyToggle,
  onStartGame,
}) => {
  if (isFinished) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>活动已结束</Text>
        <Text style={styles.infoText}>感谢您的参与！</Text>
      </View>
    );
  }

  if (!isConnected) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#007bff" />
        <Text style={styles.infoText}>正在连接到房间...</Text>
      </View>
    );
  }

  const canStartGame = isHost && members.length > 1 && members.every(m => m.is_ready);

  const renderPlayerItem = ({ item }: { item: Member }) => (
    <View style={styles.playerRow}>
      <Text style={styles.playerName}>{item.username} {item.is_host ? '👑' : ''}</Text>
      <View style={[styles.statusIndicator, item.is_ready ? styles.ready : styles.notReady]} />
      <Text style={item.is_ready ? styles.readyText : styles.notReadyText}>
        {item.is_ready ? '已准备' : '未准备'}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>等待大厅</Text>
      <FlatList
        data={members}
        renderItem={renderPlayerItem}
        keyExtractor={(item) => item.id.toString()}
        style={styles.playerList}
        ListEmptyComponent={<Text style={styles.infoText}>等待其他玩家加入...</Text>}
      />
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, isCurrentUserReady ? styles.readyButton : styles.unreadyButton]} 
          onPress={onReadyToggle}
        >
          <Text style={styles.buttonText}>{isCurrentUserReady ? '取消准备' : '准备'}</Text>
        </TouchableOpacity>
        
        {isHost && (
          <TouchableOpacity
            style={[styles.button, styles.startButton, !canStartGame && styles.disabledButton]}
            onPress={onStartGame}
            disabled={!canStartGame}
          >
            <Text style={styles.buttonText}>开始游戏</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
    container: { flex: 1, padding: 20, width: '100%' },
    title: { fontSize: 22, fontWeight: 'bold', textAlign: 'center', marginBottom: 20, color: '#333' },
    playerList: { marginBottom: 20, flexGrow: 0, width: '100%' },
    playerRow: { flexDirection: 'row', alignItems: 'center', padding: 15, backgroundColor: '#fff', borderRadius: 8, marginBottom: 10, borderWidth: 1, borderColor: '#f0f0f0' },
    playerName: { fontSize: 18, flex: 1, fontWeight: '500' },
    statusIndicator: { width: 12, height: 12, borderRadius: 6, marginRight: 8 },
    ready: { backgroundColor: '#28a745' },
    notReady: { backgroundColor: '#6c757d' },
    readyText: { fontSize: 16, color: '#28a745', fontWeight: 'bold' },
    notReadyText: { fontSize: 16, color: '#6c757d' },
    infoText: { textAlign: 'center', color: 'gray', fontSize: 16, marginTop: 20 },
    buttonContainer: { marginTop: 'auto', paddingTop: 20, borderTopWidth: 1, borderTopColor: '#eee', width: '100%' },
    button: { padding: 15, borderRadius: 8, alignItems: 'center', marginBottom: 10 },
    unreadyButton: { backgroundColor: '#007bff' },
    readyButton: { backgroundColor: '#6c757d' },
    startButton: { backgroundColor: '#28a745' },
    disabledButton: { backgroundColor: '#aaa' },
    buttonText: { color: 'white', fontSize: 18, fontWeight: 'bold' },
});