// 简单的网络连接测试工具
import { API_URL } from '../api/client';

export interface NetworkTestResult {
  success: boolean;
  message: string;
  details?: any;
  duration?: number;
}

export const testNetworkConnection = async (): Promise<NetworkTestResult> => {
  const startTime = Date.now();
  
  try {
    console.log('🔗 开始网络连接测试...');
    console.log('API URL:', API_URL);
    
    // 创建一个简单的GET请求来测试连接
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时
    
    const response = await fetch(`${API_URL}/api/`, {
      method: 'GET',
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    const duration = Date.now() - startTime;
    
    console.log('✅ 网络连接测试成功');
    console.log('响应状态:', response.status);
    console.log('响应时间:', duration + 'ms');
    
    return {
      success: true,
      message: `连接成功 (${duration}ms)`,
      details: {
        status: response.status,
        statusText: response.statusText,
        url: API_URL,
      },
      duration,
    };
    
  } catch (error: any) {
    const duration = Date.now() - startTime;
    
    console.error('❌ 网络连接测试失败:', error);
    
    let message = '连接失败';
    if (error.name === 'AbortError') {
      message = '连接超时';
    } else if (error.message.includes('Network request failed')) {
      message = '网络请求失败 - 请检查服务器是否运行';
    } else if (error.message.includes('fetch')) {
      message = '网络错误 - 请检查网络连接';
    }
    
    return {
      success: false,
      message,
      details: {
        error: error.message,
        name: error.name,
        url: API_URL,
      },
      duration,
    };
  }
};

export const testLoginAPI = async (username: string, password: string): Promise<NetworkTestResult> => {
  const startTime = Date.now();
  
  try {
    console.log('🔐 开始登录API测试...');
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await fetch(`${API_URL}/api/token/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password }),
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    const duration = Date.now() - startTime;
    
    const data = await response.json();
    
    if (response.ok && data.access) {
      console.log('✅ 登录API测试成功');
      return {
        success: true,
        message: `登录成功 (${duration}ms)`,
        details: {
          status: response.status,
          hasToken: !!data.access,
        },
        duration,
      };
    } else {
      console.log('❌ 登录API测试失败 - 无效凭据');
      return {
        success: false,
        message: '登录失败 - 无效凭据',
        details: {
          status: response.status,
          data,
        },
        duration,
      };
    }
    
  } catch (error: any) {
    const duration = Date.now() - startTime;
    
    console.error('❌ 登录API测试异常:', error);
    
    return {
      success: false,
      message: '登录API异常',
      details: {
        error: error.message,
        name: error.name,
      },
      duration,
    };
  }
};

export const testRegisterAPI = async (username: string, password: string): Promise<NetworkTestResult> => {
  const startTime = Date.now();
  
  try {
    console.log('📝 开始注册API测试...');
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
    
    const response = await fetch(`${API_URL}/api/register/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password }),
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    const duration = Date.now() - startTime;
    
    const data = await response.json();
    
    if (response.status === 201) {
      console.log('✅ 注册API测试成功');
      return {
        success: true,
        message: `注册成功 (${duration}ms)`,
        details: {
          status: response.status,
          data,
        },
        duration,
      };
    } else {
      console.log('❌ 注册API测试失败');
      return {
        success: false,
        message: '注册失败',
        details: {
          status: response.status,
          data,
        },
        duration,
      };
    }
    
  } catch (error: any) {
    const duration = Date.now() - startTime;
    
    console.error('❌ 注册API测试异常:', error);
    
    return {
      success: false,
      message: '注册API异常',
      details: {
        error: error.message,
        name: error.name,
      },
      duration,
    };
  }
};
