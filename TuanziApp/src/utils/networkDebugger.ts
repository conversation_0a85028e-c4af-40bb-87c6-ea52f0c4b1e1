// 网络调试工具
// 用于拦截和记录所有网络请求

interface RequestInfo {
  url: string;
  method: string;
  headers?: any;
  body?: any;
  timestamp: number;
}

interface ResponseInfo {
  status: number;
  statusText: string;
  headers?: any;
  body?: any;
  timestamp: number;
  duration: number;
}

class NetworkDebugger {
  private requests: Map<string, RequestInfo> = new Map();
  private responses: Map<string, ResponseInfo> = new Map();
  private originalFetch: typeof fetch;

  constructor() {
    this.originalFetch = global.fetch;
    this.setupInterceptor();
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupInterceptor() {
    global.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      const requestId = this.generateRequestId();
      const startTime = Date.now();
      
      // 记录请求信息
      const url = typeof input === 'string' ? input : input.toString();
      const method = init?.method || 'GET';
      
      const requestInfo: RequestInfo = {
        url,
        method,
        headers: init?.headers,
        body: init?.body,
        timestamp: startTime,
      };
      
      this.requests.set(requestId, requestInfo);
      
      console.log(`🌐 [${requestId}] 发起请求:`, {
        url,
        method,
        headers: init?.headers,
        body: init?.body ? (typeof init.body === 'string' ? init.body : '[非字符串数据]') : undefined,
      });

      try {
        const response = await this.originalFetch(input, init);
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // 克隆响应以便读取body
        const responseClone = response.clone();
        let responseBody;
        
        try {
          responseBody = await responseClone.text();
        } catch (e) {
          responseBody = '[无法读取响应体]';
        }
        
        const responseInfo: ResponseInfo = {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          body: responseBody,
          timestamp: endTime,
          duration,
        };
        
        this.responses.set(requestId, responseInfo);
        
        console.log(`✅ [${requestId}] 响应成功:`, {
          status: response.status,
          statusText: response.statusText,
          duration: `${duration}ms`,
          body: responseBody.length > 500 ? `${responseBody.substring(0, 500)}...` : responseBody,
        });
        
        return response;
      } catch (error) {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.error(`❌ [${requestId}] 请求失败:`, {
          error: error.message,
          duration: `${duration}ms`,
          url,
          method,
        });
        
        throw error;
      }
    };
  }

  public getRequestHistory() {
    return {
      requests: Array.from(this.requests.entries()),
      responses: Array.from(this.responses.entries()),
    };
  }

  public clearHistory() {
    this.requests.clear();
    this.responses.clear();
  }

  public restore() {
    global.fetch = this.originalFetch;
  }
}

// 创建全局实例
export const networkDebugger = new NetworkDebugger();

// 导出一些便利函数
export const getNetworkHistory = () => networkDebugger.getRequestHistory();
export const clearNetworkHistory = () => networkDebugger.clearHistory();
export const restoreOriginalFetch = () => networkDebugger.restore();
