import React, { useState } from 'react';
import { View, Text, StyleSheet, Button, Alert } from 'react-native';
import { PictionaryCanvas, PathData } from '../components/PictionaryCanvas';

/**
 * 你画我猜画板测试组件
 * 用于测试"按下、拖拽、松开画笔"的常用操作
 */
export const PictionaryCanvasTest: React.FC = () => {
  const [paths, setPaths] = useState<PathData[]>([]);
  const [isDrawer, setIsDrawer] = useState(true);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const handleDraw = (pathData: PathData) => {
    // 测试路径数据的完整性
    if (!pathData.id) {
      addTestResult('❌ 错误：路径缺少ID');
      return;
    }
    
    if (!pathData.path || pathData.path.length < 5) {
      addTestResult('❌ 错误：路径数据无效或过短');
      return;
    }
    
    if (!pathData.path.startsWith('M')) {
      addTestResult('❌ 错误：路径不是以M开头');
      return;
    }
    
    // 检查路径是否包含L命令（表示有拖拽动作）
    const hasLineCommands = pathData.path.includes(' L');
    if (hasLineCommands) {
      addTestResult('✅ 成功：检测到完整的拖拽路径');
    } else {
      addTestResult('⚠️ 警告：只检测到点击，没有拖拽');
    }
    
    setPaths(prev => [...prev, pathData]);
    addTestResult(`📝 路径已添加：ID=${pathData.id.substring(0, 8)}...`);
  };

  const clearCanvas = () => {
    setPaths([]);
    setTestResults([]);
    addTestResult('🧹 画布已清空');
  };

  const runAutomatedTest = () => {
    addTestResult('🚀 开始自动化测试...');
    
    // 模拟绘图数据
    const testPath: PathData = {
      id: `test_${Date.now()}`,
      path: 'M10,10 L20,20 L30,15 L40,25',
      color: 'blue'
    };
    
    handleDraw(testPath);
    addTestResult('🤖 自动化测试完成');
  };

  const testPathValidation = () => {
    addTestResult('🔍 开始路径验证测试...');
    
    // 测试无效路径
    const invalidPaths = [
      { id: '', path: 'M10,10', color: 'red' }, // 无ID
      { id: 'test1', path: '', color: 'red' }, // 无路径
      { id: 'test2', path: 'M', color: 'red' }, // 路径过短
      { id: 'test3', path: 'L10,10', color: 'red' }, // 不以M开头
    ];
    
    invalidPaths.forEach((path, index) => {
      addTestResult(`测试无效路径 ${index + 1}...`);
      handleDraw(path);
    });
    
    addTestResult('✅ 路径验证测试完成');
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>你画我猜画板测试</Text>
        <Text style={styles.subtitle}>
          {isDrawer ? '绘画模式：可以绘画' : '观看模式：只能查看'}
        </Text>
      </View>
      
      <View style={styles.controls}>
        <Button
          title={isDrawer ? '切换到观看模式' : '切换到绘画模式'}
          onPress={() => setIsDrawer(!isDrawer)}
        />
        <Button title="清空画布" onPress={clearCanvas} />
        <Button title="自动化测试" onPress={runAutomatedTest} />
        <Button title="路径验证测试" onPress={testPathValidation} />
      </View>
      
      <View style={styles.canvasContainer}>
        <Text style={styles.canvasTitle}>画布区域 (路径数量: {paths.length})</Text>
        <PictionaryCanvas
          isDrawer={isDrawer}
          onDraw={handleDraw}
          paths={paths}
        />
      </View>
      
      <View style={styles.logContainer}>
        <Text style={styles.logTitle}>测试日志:</Text>
        <View style={styles.logContent}>
          {testResults.slice(-10).map((result, index) => (
            <Text key={index} style={styles.logItem}>
              {result}
            </Text>
          ))}
        </View>
      </View>
      
      <View style={styles.instructions}>
        <Text style={styles.instructionTitle}>测试说明:</Text>
        <Text style={styles.instructionText}>
          1. 在绘画模式下，尝试在画布上按下、拖拽、松开画笔
        </Text>
        <Text style={styles.instructionText}>
          2. 观察是否出现"Cannot read property 'path' of null"错误
        </Text>
        <Text style={styles.instructionText}>
          3. 检查绘图延迟和分辨率表现
        </Text>
        <Text style={styles.instructionText}>
          4. 使用自动化测试验证基本功能
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 10,
    backgroundColor: '#f5f5f5',
  },
  header: {
    alignItems: 'center',
    marginBottom: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 10,
    flexWrap: 'wrap',
  },
  canvasContainer: {
    flex: 1,
    marginBottom: 10,
  },
  canvasTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
    textAlign: 'center',
  },
  logContainer: {
    height: 150,
    backgroundColor: '#fff',
    borderRadius: 5,
    padding: 10,
    marginBottom: 10,
  },
  logTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  logContent: {
    flex: 1,
  },
  logItem: {
    fontSize: 12,
    color: '#333',
    marginBottom: 2,
  },
  instructions: {
    backgroundColor: '#e8f4f8',
    padding: 10,
    borderRadius: 5,
  },
  instructionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  instructionText: {
    fontSize: 12,
    color: '#555',
    marginBottom: 2,
  },
});
