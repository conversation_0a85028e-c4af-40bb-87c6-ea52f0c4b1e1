#!/bin/bash

# 前端重置脚本
# 清理缓存并重新启动React Native应用

echo "🧹 清理前端缓存..."

cd TuanziApp

# 清理Metro缓存
echo "清理Metro缓存..."
npx react-native start --reset-cache &
METRO_PID=$!

# 等待Metro启动
sleep 5

# 杀死Metro进程
kill $METRO_PID 2>/dev/null || true

# 清理npm缓存
echo "清理npm缓存..."
npm start -- --reset-cache &
NPM_PID=$!

sleep 5
kill $NPM_PID 2>/dev/null || true

# 清理Gradle缓存（Android）
echo "清理Android缓存..."
cd android
./gradlew clean
cd ..

# 清理node_modules（如果需要）
# echo "重新安装依赖..."
# rm -rf node_modules
# npm install

echo "✅ 前端缓存清理完成"
echo "现在可以重新运行应用："
echo "  npx react-native run-android"
echo "  或"
echo "  npm start"
