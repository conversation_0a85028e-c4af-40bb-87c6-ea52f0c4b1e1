# 团子项目测试文档

本文件夹包含团子项目的所有测试文件和测试文档。

## 📁 文件结构

```
test/
├── README.md                    # 本文档
├── run_all_tests.sh            # 综合测试脚本
├── test_integration.py         # API集成测试
├── TESTING_GUIDE.md            # 测试指南
├── STABLE_TESTING_GUIDE.md     # 稳定版测试指南
└── frontend/                   # 前端测试文件
    ├── App.test.tsx
    ├── EditStepScreen.test.tsx
    └── eventApi.test.ts
```

## 🚀 快速开始

### 运行所有测试
```bash
# 在项目根目录执行
./test/run_all_tests.sh
```

### 单独运行测试

#### 后端测试
```bash
# 激活虚拟环境
source venv/bin/activate

# 运行所有后端测试
python manage.py test

# 运行特定应用的测试
python manage.py test core
python manage.py test events
python manage.py test games
```

#### 前端测试
```bash
cd TuanziApp
npm test
```

#### API集成测试
```bash
python test/test_integration.py
```

## 🧪 测试类型

### 1. 单元测试
- **位置**: 各应用的 `tests.py` 文件
- **目的**: 测试单个函数和类的功能
- **运行**: `python manage.py test`

### 2. 集成测试
- **位置**: `test/test_integration.py`
- **目的**: 测试API端点和完整流程
- **运行**: `python test/test_integration.py`

### 3. 前端测试
- **位置**: `test/frontend/`
- **目的**: 测试React Native组件和功能
- **运行**: `cd TuanziApp && npm test`

### 4. 数据库兼容性测试
- **位置**: `test/run_all_tests.sh` 中的数据库测试部分
- **目的**: 确保新增字段和模型正常工作
- **运行**: 包含在综合测试脚本中

## 🔧 测试环境设置

### 后端测试环境
1. 确保虚拟环境已激活
2. 确保所有依赖已安装: `pip install -r requirements.txt`
3. 确保数据库迁移已应用: `python manage.py migrate`

### 前端测试环境
1. 确保Node.js依赖已安装: `cd TuanziApp && npm install`
2. 确保Jest配置正确

## 📊 测试覆盖范围

### 已测试功能
- ✅ 用户认证 (登录/注册)
- ✅ 房间创建和加入
- ✅ 签到功能
- ✅ 虚拟货币系统
- ✅ 环节模板管理
- ✅ 数据库模型关系

### 待测试功能
- ⏳ WebSocket连接
- ⏳ 游戏逻辑
- ⏳ 权限管理
- ⏳ 错误处理

## 🐛 故障排除

### 常见问题

#### 数据库错误
```bash
# 重置数据库
rm db.sqlite3
python manage.py migrate
```

#### 前端测试失败
```bash
# 清理缓存
cd TuanziApp
npm run clean
npm install
```

#### API测试失败
```bash
# 确保后端服务器运行
source venv/bin/activate
daphne -b 0.0.0.0 -p 8000 Tuanzi_Backend.asgi:application
```

## 📝 添加新测试

### 后端测试
在相应应用的 `tests.py` 文件中添加测试类和方法。

### 前端测试
在 `test/frontend/` 目录下创建新的测试文件。

### API测试
在 `test/test_integration.py` 中添加新的测试方法。

## 🔄 持续集成

建议在每次代码提交前运行完整测试套件：
```bash
./test/run_all_tests.sh
```

## 📚 相关文档

- [TESTING_GUIDE.md](./TESTING_GUIDE.md) - 详细测试指南
- [STABLE_TESTING_GUIDE.md](./STABLE_TESTING_GUIDE.md) - 稳定版测试指南
- [../README.md](../README.md) - 项目主文档
