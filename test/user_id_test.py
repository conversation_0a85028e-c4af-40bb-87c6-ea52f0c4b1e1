#!/usr/bin/env python3
"""
用户ID测试脚本
验证JWT token中的用户ID是否正确传递到前端
"""

import requests
import json
from jwt import decode

API_URL = "http://192.168.0.225:8000"

def test_user_id_in_token():
    print("🧪 测试JWT token中的用户ID...")
    
    # 1. 登录获取token
    login_response = requests.post(f"{API_URL}/api/token/", {
        'username': 'testready1',
        'password': 'test123'
    })
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return False
    
    token_data = login_response.json()
    access_token = token_data['access']
    
    print(f"✅ 登录成功，获取token")
    
    # 2. 解码token查看内容
    try:
        # 注意：这里我们不验证签名，只是查看内容
        decoded = decode(access_token, options={"verify_signature": False})
        print(f"📋 Token内容:")
        print(f"   user_id: {decoded.get('user_id')}")
        print(f"   username: {decoded.get('username')}")
        print(f"   exp: {decoded.get('exp')}")
        print(f"   iat: {decoded.get('iat')}")
        
        if 'user_id' in decoded and 'username' in decoded:
            print("✅ Token包含必要的用户信息")
            return True
        else:
            print("❌ Token缺少用户信息")
            return False
            
    except Exception as e:
        print(f"❌ 解码token失败: {e}")
        return False

def test_user_status_api():
    print("\n🧪 测试用户状态API...")
    
    # 登录获取token
    login_response = requests.post(f"{API_URL}/api/token/", {
        'username': 'testready1',
        'password': 'test123'
    })
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return False
    
    token = login_response.json()['access']
    
    # 获取用户状态
    status_response = requests.get(f"{API_URL}/api/user/status/", 
                                 headers={'Authorization': f'Bearer {token}'})
    
    if status_response.status_code != 200:
        print(f"❌ 获取用户状态失败: {status_response.status_code}")
        return False
    
    status_data = status_response.json()
    print(f"📋 用户状态API返回:")
    print(f"   logged_in: {status_data.get('logged_in')}")
    print(f"   user: {status_data.get('user')}")
    
    user_info = status_data.get('user', {})
    if 'id' in user_info and 'username' in user_info:
        print("✅ 用户状态API包含用户ID")
        return True
    else:
        print("❌ 用户状态API缺少用户ID")
        return False

def main():
    print("🚀 开始用户ID测试")
    
    test1_result = test_user_id_in_token()
    test2_result = test_user_status_api()
    
    print(f"\n📊 测试结果:")
    print(f"JWT Token测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"用户状态API测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("🎉 所有测试通过！用户ID正确传递。")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False

if __name__ == "__main__":
    try:
        result = main()
        exit(0 if result else 1)
    except Exception as e:
        print(f"测试异常: {e}")
        exit(1)
