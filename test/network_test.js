// 网络连接测试脚本
// 用于测试前端是否能够连接到后端API

const API_URL = 'http://192.168.0.225:8000';

async function testConnection() {
    console.log('🔗 测试网络连接...');
    console.log('API URL:', API_URL);
    
    try {
        // 测试基本连接
        console.log('\n1. 测试基本连接...');
        const healthResponse = await fetch(`${API_URL}/api/`, {
            method: 'GET',
        });
        console.log('健康检查状态:', healthResponse.status);
        
        // 测试登录API
        console.log('\n2. 测试登录API...');
        const loginResponse = await fetch(`${API_URL}/api/token/`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username: 'test', password: 'test' }),
        });
        console.log('登录API状态:', loginResponse.status);
        const loginData = await loginResponse.json();
        console.log('登录响应:', loginData);
        
        // 测试注册API
        console.log('\n3. 测试注册API...');
        const registerResponse = await fetch(`${API_URL}/api/register/`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username: 'testuser_network', password: 'testpass123' }),
        });
        console.log('注册API状态:', registerResponse.status);
        const registerData = await registerResponse.json();
        console.log('注册响应:', registerData);
        
        console.log('\n✅ 网络连接测试完成');
        
    } catch (error) {
        console.error('❌ 网络连接测试失败:', error);
    }
}

// 运行测试
testConnection();
