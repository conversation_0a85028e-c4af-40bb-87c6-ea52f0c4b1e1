#!/bin/bash

# 团子项目综合测试脚本
# 用于测试后端API、前端功能和数据库兼容性

set -e  # 遇到错误立即退出

echo "🚀 开始团子项目综合测试..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查是否在项目根目录
if [ ! -f "manage.py" ]; then
    echo -e "${RED}❌ 请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 激活虚拟环境
echo -e "${YELLOW}📦 激活虚拟环境...${NC}"
source venv/bin/activate

# 1. 数据库测试
echo -e "${YELLOW}🗄️  测试数据库迁移和模型...${NC}"
python manage.py check
python manage.py makemigrations --check --dry-run
echo -e "${GREEN}✅ 数据库检查通过${NC}"

# 2. 后端单元测试
echo -e "${YELLOW}🧪 运行后端单元测试...${NC}"
python manage.py test core.tests -v 2
python manage.py test events.tests -v 2
python manage.py test games.tests -v 2
echo -e "${GREEN}✅ 后端单元测试通过${NC}"

# 3. API集成测试
echo -e "${YELLOW}🔗 运行API集成测试...${NC}"
if [ -f "test/test_integration.py" ]; then
    python test/test_integration.py
    echo -e "${GREEN}✅ API集成测试通过${NC}"
else
    echo -e "${YELLOW}⚠️  API集成测试文件不存在，跳过${NC}"
fi

# 4. 前端测试
echo -e "${YELLOW}📱 运行前端测试...${NC}"
cd TuanziApp
if [ -f "package.json" ]; then
    npm test -- --watchAll=false
    echo -e "${GREEN}✅ 前端测试通过${NC}"
else
    echo -e "${YELLOW}⚠️  前端package.json不存在，跳过前端测试${NC}"
fi
cd ..

# 5. 数据库兼容性测试
echo -e "${YELLOW}🔄 测试数据库兼容性...${NC}"
python manage.py shell << EOF
from core.models import User, CheckIn, Room, RoomMembership
from events.models import EventTemplate, EventStep

# 测试新增字段
print("测试User.currency字段...")
user = User.objects.first()
if user:
    print(f"用户 {user.username} 的货币: {user.currency}")
else:
    print("没有用户数据")

# 测试新模型
print("测试CheckIn模型...")
checkins = CheckIn.objects.all()
print(f"签到记录数量: {checkins.count()}")

print("测试RoomMembership模型...")
memberships = RoomMembership.objects.all()
print(f"房间成员关系数量: {memberships.count()}")

print("数据库兼容性测试完成")
EOF
echo -e "${GREEN}✅ 数据库兼容性测试通过${NC}"

echo -e "${GREEN}🎉 所有测试完成！${NC}"
echo -e "${GREEN}📊 测试总结:${NC}"
echo -e "  ✅ 数据库检查"
echo -e "  ✅ 后端单元测试"
echo -e "  ✅ API集成测试"
echo -e "  ✅ 前端测试"
echo -e "  ✅ 数据库兼容性测试"
