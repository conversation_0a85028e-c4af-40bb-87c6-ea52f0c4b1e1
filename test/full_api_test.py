#!/usr/bin/env python3
"""
完整的前后端API测试脚本
模拟前端的所有API调用，验证后端响应
"""

import requests
import json
import time
import sys

# 配置
API_URL = "http://192.168.0.225:8000"
TEST_USERNAME = f"testuser_{int(time.time())}"
TEST_PASSWORD = "testpass123"

class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    END = '\033[0m'

def log(message, color=Colors.BLUE):
    print(f"{color}{message}{Colors.END}")

def test_api_endpoint(name, method, url, data=None, headers=None, expected_status=200):
    """测试API端点"""
    log(f"\n🧪 测试 {name}...")
    log(f"   URL: {method} {url}")
    if data:
        log(f"   数据: {json.dumps(data, ensure_ascii=False)}")
    
    try:
        start_time = time.time()
        
        if method == 'GET':
            response = requests.get(url, headers=headers, timeout=30)
        elif method == 'POST':
            response = requests.post(url, json=data, headers=headers, timeout=30)
        else:
            log(f"❌ 不支持的HTTP方法: {method}", Colors.RED)
            return False
        
        end_time = time.time()
        duration = (end_time - start_time) * 1000
        
        log(f"   响应时间: {duration:.2f}ms")
        log(f"   状态码: {response.status_code}")
        
        try:
            response_data = response.json()
            log(f"   响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
        except:
            log(f"   响应文本: {response.text[:200]}...")
        
        if response.status_code == expected_status:
            log(f"✅ {name} 测试通过", Colors.GREEN)
            return True, response
        else:
            log(f"❌ {name} 测试失败 - 期望状态码 {expected_status}, 实际 {response.status_code}", Colors.RED)
            return False, response
            
    except requests.exceptions.Timeout:
        log(f"❌ {name} 测试超时", Colors.RED)
        return False, None
    except requests.exceptions.ConnectionError:
        log(f"❌ {name} 连接错误", Colors.RED)
        return False, None
    except Exception as e:
        log(f"❌ {name} 测试异常: {e}", Colors.RED)
        return False, None

def main():
    log("🚀 开始完整的前后端API测试", Colors.BLUE)
    log(f"API基础URL: {API_URL}")
    log(f"测试用户名: {TEST_USERNAME}")
    
    results = []
    token = None
    
    # 1. 测试用户注册
    success, response = test_api_endpoint(
        "用户注册",
        "POST",
        f"{API_URL}/api/register/",
        {"username": TEST_USERNAME, "password": TEST_PASSWORD},
        expected_status=201
    )
    results.append(("用户注册", success))
    
    # 2. 测试用户登录
    success, response = test_api_endpoint(
        "用户登录",
        "POST", 
        f"{API_URL}/api/token/",
        {"username": TEST_USERNAME, "password": TEST_PASSWORD},
        expected_status=200
    )
    results.append(("用户登录", success))
    
    if success and response:
        try:
            token_data = response.json()
            token = token_data.get('access')
            log(f"   获取到访问令牌: {token[:50]}...", Colors.GREEN)
        except:
            log("   无法解析登录响应", Colors.RED)
    
    # 3. 测试用户状态（需要认证）
    if token:
        headers = {"Authorization": f"Bearer {token}"}
        success, response = test_api_endpoint(
            "用户状态",
            "GET",
            f"{API_URL}/api/user/status/",
            headers=headers,
            expected_status=200
        )
        results.append(("用户状态", success))
    else:
        log("⚠️ 跳过用户状态测试 - 没有有效令牌", Colors.YELLOW)
        results.append(("用户状态", False))
    
    # 4. 测试签到状态
    if token:
        headers = {"Authorization": f"Bearer {token}"}
        success, response = test_api_endpoint(
            "签到状态",
            "GET",
            f"{API_URL}/api/check-in/status/",
            headers=headers,
            expected_status=200
        )
        results.append(("签到状态", success))
    else:
        log("⚠️ 跳过签到状态测试 - 没有有效令牌", Colors.YELLOW)
        results.append(("签到状态", False))
    
    # 5. 测试执行签到
    if token:
        headers = {"Authorization": f"Bearer {token}"}
        success, response = test_api_endpoint(
            "执行签到",
            "POST",
            f"{API_URL}/api/check-in/",
            headers=headers,
            expected_status=201
        )
        results.append(("执行签到", success))
    else:
        log("⚠️ 跳过执行签到测试 - 没有有效令牌", Colors.YELLOW)
        results.append(("执行签到", False))
    
    # 6. 测试事件模板列表
    if token:
        headers = {"Authorization": f"Bearer {token}"}
        success, response = test_api_endpoint(
            "事件模板列表",
            "GET",
            f"{API_URL}/api/events/templates/",
            headers=headers,
            expected_status=200
        )
        results.append(("事件模板列表", success))
    else:
        log("⚠️ 跳过事件模板列表测试 - 没有有效令牌", Colors.YELLOW)
        results.append(("事件模板列表", False))
    
    # 测试结果汇总
    log("\n📊 测试结果汇总:", Colors.BLUE)
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        color = Colors.GREEN if success else Colors.RED
        log(f"   {test_name}: {status}", color)
        if success:
            passed += 1
    
    log(f"\n🏁 测试完成: {passed}/{total} 通过", Colors.GREEN if passed == total else Colors.YELLOW)
    
    if passed == total:
        log("🎉 所有API测试通过！前后端连接正常。", Colors.GREEN)
        return True
    else:
        log("⚠️ 部分API测试失败，请检查后端服务和网络连接。", Colors.YELLOW)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
