#!/usr/bin/env python3
"""
API兼容性测试脚本
用于验证前端和后端API的一致性
"""

import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

BASE_URL = "http://localhost:8000"

class APICompatibilityTest:
    def __init__(self):
        self.token = None
        self.room_code = None
        self.template_id = None
        
    def log(self, message, status="INFO"):
        colors = {
            "INFO": "\033[94m",
            "SUCCESS": "\033[92m", 
            "ERROR": "\033[91m",
            "WARNING": "\033[93m"
        }
        print(f"{colors.get(status, '')}{status}: {message}\033[0m")
    
    def test_user_registration(self):
        """测试用户注册API"""
        self.log("测试用户注册...")
        
        response = requests.post(f"{BASE_URL}/api/register/", {
            'username': 'testuser_api',
            'password': 'testpass123'
        })
        
        if response.status_code == 201:
            self.log("用户注册成功", "SUCCESS")
            return True
        elif response.status_code == 400 and "already exists" in response.text:
            self.log("用户已存在，跳过注册", "WARNING")
            return True
        else:
            self.log(f"用户注册失败: {response.status_code} - {response.text}", "ERROR")
            return False
    
    def test_user_login(self):
        """测试用户登录API"""
        self.log("测试用户登录...")
        
        response = requests.post(f"{BASE_URL}/api/token/", {
            'username': 'testuser_api',
            'password': 'testpass123'
        })
        
        if response.status_code == 200:
            data = response.json()
            self.token = data.get('access')
            self.log("用户登录成功", "SUCCESS")
            return True
        else:
            self.log(f"用户登录失败: {response.status_code} - {response.text}", "ERROR")
            return False
    
    def test_user_status(self):
        """测试用户状态API"""
        self.log("测试用户状态...")
        
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.get(f"{BASE_URL}/api/user/status/", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('logged_in') and 'user' in data:
                self.log("用户状态获取成功", "SUCCESS")
                return True
        
        self.log(f"用户状态获取失败: {response.status_code} - {response.text}", "ERROR")
        return False
    
    def test_check_in_status(self):
        """测试签到状态API"""
        self.log("测试签到状态...")
        
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.get(f"{BASE_URL}/api/check-in/status/", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if 'checked_in_today' in data:
                self.log("签到状态获取成功", "SUCCESS")
                return True
        
        self.log(f"签到状态获取失败: {response.status_code} - {response.text}", "ERROR")
        return False
    
    def test_check_in(self):
        """测试签到API"""
        self.log("测试签到...")
        
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.post(f"{BASE_URL}/api/check-in/", headers=headers)
        
        if response.status_code in [200, 201]:
            data = response.json()
            if data.get('success'):
                self.log("签到成功", "SUCCESS")
                return True
        elif response.status_code == 409 and "已经签到" in response.text:
            self.log("今日已签到，跳过", "WARNING")
            return True

        self.log(f"签到失败: {response.status_code} - {response.text}", "ERROR")
        return False
    
    def test_event_templates(self):
        """测试事件模板API"""
        self.log("测试事件模板...")
        
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.get(f"{BASE_URL}/api/events/templates/", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, list):
                if len(data) > 0:
                    self.template_id = data[0]['id']
                self.log("事件模板获取成功", "SUCCESS")
                return True
        
        self.log(f"事件模板获取失败: {response.status_code} - {response.text}", "ERROR")
        return False
    
    def test_room_creation(self):
        """测试房间创建API"""
        if not self.template_id:
            self.log("跳过房间创建测试 - 没有可用模板", "WARNING")
            return True
            
        self.log("测试房间创建...")
        
        headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }
        response = requests.post(f"{BASE_URL}/api/rooms/create/", 
                               json={'template_id': self.template_id}, 
                               headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if 'room_code' in data:
                self.room_code = data['room_code']
                self.log("房间创建成功", "SUCCESS")
                return True
        
        self.log(f"房间创建失败: {response.status_code} - {response.text}", "ERROR")
        return False
    
    def test_room_join(self):
        """测试加入房间API"""
        if not self.room_code:
            self.log("跳过加入房间测试 - 没有可用房间", "WARNING")
            return True
            
        self.log("测试加入房间...")
        
        headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }
        response = requests.post(f"{BASE_URL}/api/rooms/join/", 
                               json={'room_code': self.room_code}, 
                               headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if 'room_code' in data:
                self.log("加入房间成功", "SUCCESS")
                return True
        
        self.log(f"加入房间失败: {response.status_code} - {response.text}", "ERROR")
        return False
    
    def run_all_tests(self):
        """运行所有API兼容性测试"""
        self.log("开始API兼容性测试", "INFO")
        
        tests = [
            self.test_user_registration,
            self.test_user_login,
            self.test_user_status,
            self.test_check_in_status,
            self.test_check_in,
            self.test_event_templates,
            self.test_room_creation,
            self.test_room_join,
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                self.log(f"测试异常: {e}", "ERROR")
        
        self.log(f"测试完成: {passed}/{total} 通过", "SUCCESS" if passed == total else "WARNING")
        return passed == total

if __name__ == "__main__":
    tester = APICompatibilityTest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
