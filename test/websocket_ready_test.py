#!/usr/bin/env python3
"""
WebSocket准备功能测试脚本
模拟前端的准备按钮点击
"""

import asyncio
import websockets
import json
import requests
import sys

API_URL = "http://192.168.0.225:8000"
WS_URL = "ws://192.168.0.225:8000"

async def test_ready_functionality():
    print("🧪 开始WebSocket准备功能测试...")
    
    # 1. 登录获取token
    print("1. 登录用户...")
    login_response = requests.post(f"{API_URL}/api/token/", {
        'username': 'testready1',
        'password': 'test123'
    })
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return False
    
    token = login_response.json()['access']
    print(f"✅ 登录成功，获取token: {token[:50]}...")
    
    # 2. 连接WebSocket
    room_code = "TESTREADY"
    ws_url = f"{WS_URL}/ws/room/{room_code}/?token={token}"
    print(f"2. 连接WebSocket: {ws_url}")
    
    try:
        async with websockets.connect(ws_url) as websocket:
            print("✅ WebSocket连接成功")
            
            # 3. 等待初始房间状态
            print("3. 等待初始房间状态...")
            initial_message = await websocket.recv()
            initial_data = json.loads(initial_message)
            print(f"收到初始消息: {initial_data}")
            
            # 4. 发送准备消息
            print("4. 发送准备消息...")
            ready_message = {
                "action": "set_ready",
                "payload": {"is_ready": True}
            }
            await websocket.send(json.dumps(ready_message))
            print(f"发送消息: {ready_message}")
            
            # 5. 等待响应
            print("5. 等待响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                response_data = json.loads(response)
                print(f"收到响应: {response_data}")
                
                # 检查是否有房间状态更新
                if response_data.get('type') == 'room_state_update':
                    members = response_data.get('payload', {}).get('members', [])
                    ready_members = [m for m in members if m.get('is_ready')]
                    print(f"✅ 房间状态更新成功，已准备成员: {[m['username'] for m in ready_members]}")
                    return True
                else:
                    print(f"⚠️ 收到意外响应类型: {response_data.get('type')}")
                    return False
                    
            except asyncio.TimeoutError:
                print("❌ 等待响应超时")
                return False
                
    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ WebSocket连接关闭: {e}")
        return False
    except Exception as e:
        print(f"❌ WebSocket连接错误: {e}")
        return False

async def test_ready_toggle():
    """测试准备状态切换"""
    print("\n🔄 测试准备状态切换...")
    
    # 登录
    login_response = requests.post(f"{API_URL}/api/token/", {
        'username': 'testready1',
        'password': 'test123'
    })
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return False
    
    token = login_response.json()['access']
    room_code = "TESTREADY"
    ws_url = f"{WS_URL}/ws/room/{room_code}/?token={token}"
    
    try:
        async with websockets.connect(ws_url) as websocket:
            print("✅ WebSocket连接成功")
            
            # 等待初始消息
            await websocket.recv()
            
            # 测试准备 -> 取消准备 -> 再次准备
            for i, ready_state in enumerate([True, False, True]):
                print(f"步骤 {i+1}: 设置准备状态为 {ready_state}")
                
                message = {
                    "action": "set_ready",
                    "payload": {"is_ready": ready_state}
                }
                await websocket.send(json.dumps(message))
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    
                    if response_data.get('type') == 'room_state_update':
                        members = response_data.get('payload', {}).get('members', [])
                        current_user = next((m for m in members if m['username'] == 'testready1'), None)
                        
                        if current_user and current_user['is_ready'] == ready_state:
                            print(f"✅ 准备状态更新成功: {ready_state}")
                        else:
                            print(f"❌ 准备状态更新失败，期望: {ready_state}, 实际: {current_user['is_ready'] if current_user else 'None'}")
                            return False
                    else:
                        print(f"⚠️ 收到意外响应: {response_data}")
                        
                except asyncio.TimeoutError:
                    print(f"❌ 步骤 {i+1} 超时")
                    return False
                    
                # 短暂延迟
                await asyncio.sleep(1)
            
            print("✅ 准备状态切换测试完成")
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def main():
    print("🚀 开始WebSocket准备功能完整测试")
    
    # 测试基本准备功能
    test1_result = await test_ready_functionality()
    
    # 测试准备状态切换
    test2_result = await test_ready_toggle()
    
    # 结果汇总
    print(f"\n📊 测试结果:")
    print(f"基本准备功能: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"准备状态切换: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试异常: {e}")
        sys.exit(1)
